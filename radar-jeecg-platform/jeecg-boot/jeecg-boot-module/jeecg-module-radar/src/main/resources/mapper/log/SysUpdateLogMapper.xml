<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.log.mapper.SysUpdateLogMapper">
    <resultMap id="BaseResultMap" type="org.jeecg.modules.log.entity.SysUpdateLog">
        <id column="log_id" property="logId" />
        <result column="user_id" property="userId" />
        <result column="run_time" property="runTime" />
        <result column="log_content" property="logContent" />
        <result column="run_result" property="runResult" />
        <result column="error_cause" property="errorCause" />
        <result column="del_flag" property="delFlag" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remark" property="remark" />
    </resultMap>
</mapper>


