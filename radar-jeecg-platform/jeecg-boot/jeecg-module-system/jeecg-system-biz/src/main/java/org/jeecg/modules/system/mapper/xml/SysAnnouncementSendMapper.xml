<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.system.mapper.SysAnnouncementSendMapper">
	
	<resultMap id="AnnouncementSendModel" type="org.jeecg.modules.system.model.AnnouncementSendModel" >
		<result column="id" property="id" jdbcType="VARCHAR"/>
		<result column="annt_id" property="anntId" jdbcType="VARCHAR"/>
		<result column="user_id" property="userId" jdbcType="VARCHAR"/>
		<result column="titile" property="titile" jdbcType="VARCHAR"/>
		<result column="msg_content" property="msgContent" jdbcType="VARCHAR"/>
		<result column="sender" property="sender" jdbcType="VARCHAR"/>
		<result column="priority" property="priority" jdbcType="VARCHAR"/>
		<result column="msg_category" property="msgCategory" jdbcType="VARCHAR"/>
		<result column="send_time" property="sendTime" jdbcType="TIMESTAMP"/>
		<result column="bus_id" property="busId" jdbcType="VARCHAR"/>
		<result column="bus_type" property="busType" jdbcType="VARCHAR"/>
		<result column="open_type" property="openType" jdbcType="VARCHAR"/>
		<result column="open_page" property="openPage" jdbcType="VARCHAR"/>
	</resultMap>
	
	<select id="getMyAnnouncementSendList" parameterType="Object"  resultMap="AnnouncementSendModel">
	   select
	   		sas.id,
	   		sas.annt_id,
	   		sas.user_id,
	   		sas.read_flag,
	   		sa.titile as titile,
	   		sa.msg_content as msg_content,
	   		sa.sender as sender,
	   		sa.priority as priority,
	   		sa.msg_category,
	   		sa.send_time as send_time,
	   		sa.bus_id as bus_id,
	   		sa.bus_type as bus_type,
			sa.open_type as open_type,
			sa.open_page as open_page,
			sa.msg_abstract
	   from sys_announcement_send sas
	   left join sys_announcement sa ON sas.annt_id = sa.id
	   where sa.send_status = '1'
	   and sa.del_flag = '0'
	   and sas.user_id = #{announcementSendModel.userId}
	   <if test="announcementSendModel.titile !=null and announcementSendModel.titile != ''">
	   		and sa.titile LIKE concat(concat('%',#{announcementSendModel.titile}),'%')
	   </if>
	   <if test="announcementSendModel.sender !=null and announcementSendModel.sender != ''">
	   		and sa.sender LIKE concat(concat('%',#{announcementSendModel.sender}),'%')
	   </if>
	   <if test="announcementSendModel.readFlag !=null">
	   		and sas.read_flag = #{announcementSendModel.readFlag}
	   </if>
	   <if test="announcementSendModel.busType !=null and announcementSendModel.busType != ''">
	   		and sa.bus_type = #{announcementSendModel.busType}
	   </if>
		<if test="announcementSendModel.bizSource !=null and announcementSendModel.bizSource =='isNoBpm'">
			and (sa.bus_type != 'bpm' or sa.bus_type is null)
		</if>
        <if test="announcementSendModel.msgCategory !=null and announcementSendModel.msgCategory != ''">
            and sa.msg_category = #{announcementSendModel.msgCategory}
        </if>
		<if test="announcementSendModel.sendTimeBegin !=null and announcementSendModel.sendTimeBegin != '' 
		and announcementSendModel.sendTimeEnd != '' and announcementSendModel.sendTimeEnd != ''">
			and sa.send_time between #{announcementSendModel.sendTimeBegin} and #{announcementSendModel.sendTimeEnd}
		</if>
	   order by sas.read_flag,sa.send_time desc
	</select>



	<!-- 查询一条消息 -->
	<select id="getOne" parameterType="String" resultMap="AnnouncementSendModel">
		select
		sas.id,
		sas.annt_id,
		sas.user_id,
		sas.read_flag,
		sa.titile as titile,
		sa.msg_content as msg_content,
		sa.sender as sender,
		sa.priority as priority,
		sa.msg_category,
		sa.send_time as send_time,
		sa.bus_id as bus_id,
		sa.open_type as open_type,
		sa.open_page as open_page,
		sa.msg_abstract
		from sys_announcement_send sas
		left join sys_announcement sa ON sas.annt_id = sa.id
		where sa.send_status = '1'
		and sa.del_flag = '0'
		and sas.id = #{sendId}
	</select>


	<!-- 修改为已读消息 -->
	<update id="updateReaded">
		update sys_announcement_send set read_flag = 1
		 where user_id = #{userId} 
			and annt_id in
		<foreach collection="annoceIdList" index="index" item="id" open="(" separator="," close=")">
			#{id}
		</foreach>
	</update>
	
	
	<!--清除所有未读消息-->
	<update id="clearAllUnReadMessage">
		update sys_announcement_send set read_flag = 1
		where user_id = #{userId} and read_flag = 0
	</update>

</mapper>