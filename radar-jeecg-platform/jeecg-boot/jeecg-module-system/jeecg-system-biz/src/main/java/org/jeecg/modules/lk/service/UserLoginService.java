package org.jeecg.modules.lk.service;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.lk.vo.UserLoginRequest;
import org.jeecg.modules.lk.vo.UserLoginResponse;
import org.jeecg.modules.system.entity.SysUser;
import org.jeecg.modules.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * @Description: 用户登录服务实现类
 * @Author: assistant
 * @Date: 2025
 * @Version: V1.0
 */
@Service
@Slf4j
public class UserLoginService {

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private RedisUtil redisUtil;
    
    /**
     * 用户登录方法
     * 
     * @param loginRequest 登录请求参数
     * @return 登录结果
     */
    public UserLoginResponse login(UserLoginRequest loginRequest) {
        log.info("开始用户登录流程，手机号: {}", loginRequest.getPhone());
        
        // 校验验证码（占位符）
        if (!validateCaptcha(loginRequest.getCaptcha())) {
            log.warn("验证码校验失败");
            throw new JeecgBootException("验证码校验失败");
        }
        
        // 这里假设我们通过手机号查询用户
        SysUser user = sysUserService.getOne(
            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<SysUser>()
                .eq("phone", loginRequest.getPhone())
        );
        
        if (user == null) {
            log.warn("用户不存在，手机号: {}", loginRequest.getPhone());
            throw new JeecgBootException("用户不存在");
        }
        
        // 校验密码是否正确
//        if (!StringUtils.hasText(user.getPassword()) ||
//            !user.getPassword().equals(loginRequest.getPassword())) {
//            log.warn("用户密码不正确，手机号: {}", loginRequest.getPhone());
//            throw new JeecgBootException("用户密码不正确");
//        }

        /*
         * 这一段暂且从 LoginController.java 453行的方法中的467行复制过来，它有一个用户的缓存，但是缓存的更新点全！部！都只有修改用户信息的时候，
         * 所以它每次登录都要触发一次修改方法才能创建这个缓存，否则 ShiroRealm.java：130方法中的TokenUtils.java:161会报错找不到这个缓存内容
         */
        sysUserService.setLoginTenant(user, new JSONObject(), user.getUsername(), null);

        // 生成JWT Token
        String token = generateJwtToken(user);
        log.info("生成JWT Token: {}", token);
        
        log.info("用户登录成功，手机号: {}", loginRequest.getPhone());

        return UserLoginResponse.builder().userId(user.getId()).token(token).build();
    }
    
    /**
     * 生成JWT Token
     * 
     * @param user 用户信息
     * @return JWT Token
     */
    private String generateJwtToken(SysUser user) {
        // 使用JwtUtil工具类生成token，它现在说是默认过期时间7天，但是我不理解的是，它以前一直用用户名当查询条件，，用户名啊，，用户名，，user name
        String token = org.jeecg.common.system.util.JwtUtil.sign(user.getUsername(), user.getPassword());
        // 设置token缓存有效时间
        redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
        redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME * 2 / 1000);
        return token;
    }
    
    /**
     * 验证码校验（占位符方法）
     * 
     * @param captcha 验证码
     * @return 校验结果
     */
    private boolean validateCaptcha(String captcha) {
        // TODO: 实现验证码校验逻辑
        // 这里只是一个占位符，实际应该从缓存中获取验证码进行比对，或者调用验证码服务进行验证
        log.info("验证码校验占位符方法，验证码: {}", captcha);
        return StringUtils.hasText(captcha); // 简单校验验证码不为空
    }
}