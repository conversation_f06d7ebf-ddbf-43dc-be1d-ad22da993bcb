package org.jeecg.modules.lk.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.lk.service.UserLoginService;
import org.jeecg.modules.lk.vo.UserLoginResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * @Description: 股税宝微信小程序登录控制器
 * @Author: assistant
 * @Date: 2025
 * @Version: V1.0
 */
@Tag(name = "乐凯登录")
@RestController
@RequestMapping("/lk/user/login")
@Slf4j
public class LkUserLoginController {

    @Autowired
    private UserLoginService userLoginService;

    /**
     * 手机号密码登录接口
     *
     * @param loginRequest 登录请求参数
     * @return 登录结果
     */
    @Operation(summary = "手机号登录")
    @PostMapping("/phoneLogin")
    public Result<UserLoginResponse> phoneLogin(@RequestBody org.jeecg.modules.lk.vo.UserLoginRequest loginRequest) {
        log.info("手机号登录接口调用，手机号：{}", loginRequest.getPhone());

        try {
            // 调用登录服务进行登录
            UserLoginResponse userLoginResponse = userLoginService.login(loginRequest);

            log.info("用户手机号登录成功，手机号：{}", loginRequest.getPhone());
            return Result.OK("登录成功", userLoginResponse);

        } catch (Exception e) {
            log.error("手机号登录异常：", e);
            return Result.error("登录失败：" + e.getMessage());
        }
    }

} 