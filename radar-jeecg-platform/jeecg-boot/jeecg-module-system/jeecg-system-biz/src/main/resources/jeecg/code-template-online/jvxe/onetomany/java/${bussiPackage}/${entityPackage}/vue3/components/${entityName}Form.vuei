<#include "/common/utils.ftl">
<template>
  <div>
    <BasicForm @register="registerForm" ref="formRef"/>
    <!-- 子表单区域 -->
    <a-tabs v-model:activeKey="activeKey" animated  @change="handleChangeTabs">
    <#list subTables as sub><#rt/>
      <#assign refKey = sub.entityName?uncap_first/>
      <#if sub.foreignRelationType =='1'>
      <a-tab-pane tab="${sub.ftlDescription}" key="${refKey}" :forceRender="true">
        <${sub.entityName}Form ref="${sub.entityName?uncap_first}Form" :disabled="formDisabled"></${sub.entityName}Form>
      </a-tab-pane>
      <#else>
      <a-tab-pane tab="${sub.ftlDescription}" key="${refKey}" :forceRender="true">
        <JVxeTable
          keep-source
          resizable
          ref="${refKey}"
          v-if="${sub.entityName?uncap_first}Table.show"
          :loading="${sub.entityName?uncap_first}Table.loading"
          :columns="${sub.entityName?uncap_first}Table.columns"
          :dataSource="${sub.entityName?uncap_first}Table.dataSource"
          :height="340"
          :rowNumber="true"
          :rowSelection="true"
          :disabled="formDisabled"
          :toolbar="true"
        />
      </a-tab-pane>
      </#if>
    </#list>
    </a-tabs>

    <div style="width: 100%;text-align: center" v-if="!formDisabled">
      <a-button @click="handleSubmit" pre-icon="ant-design:check" type="primary">提 交</a-button>
    </div>
  </div>
</template>

<script lang="ts">

  import {BasicForm, useForm} from '/@/components/Form/index';
  import { computed, defineComponent, reactive, ref, unref } from 'vue';
  import {defHttp} from '/@/utils/http/axios';
  import { propTypes } from '/@/utils/propTypes';
  import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods';
  import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils';
  <#list subTables as sub>
  <#if sub.foreignRelationType =='1'>
  import ${sub.entityName}Form from './${sub.entityName}Form.vue'
  </#if>
  </#list>
  import {getBpmFormSchema<#list subTables as sub><#if sub.foreignRelationType =='0'>,${sub.entityName?uncap_first}Columns</#if></#list>} from '../${entityName}.data';
  import {saveOrUpdate<#list subTables as sub>,${sub.entityName?uncap_first}List</#list>} from '../${entityName}.api';

  export default defineComponent({
    name: "${entityName}Form",
    components:{
      BasicForm,
      <#list subTables as sub>
      <#if sub.foreignRelationType =='1'>
      ${sub.entityName}Form,
      </#if>
      </#list>
    },
    props:{
      formData: propTypes.object.def({}),
      formBpm: propTypes.bool.def(true),
    },
    setup(props){
      const [registerForm, { setFieldsValue, setProps }] = useForm({
        labelWidth: 150,
        schemas: getBpmFormSchema(props.formData),
        showActionButtonGroup: false,
        baseColProps: {span: ${getFormSpan(tableVo.fieldRowNum?default(1))}}
      });

      const formDisabled = computed(()=>{
        if(props.formData.disabled === false){
          return false;
        }
        return true;
      });

      <#assign hasOne2Many = false>
      <#assign hasOne2One = false>
      const refKeys = ref([<#list subTables as sub>'${sub.entityName?uncap_first}', </#list>]);
      const activeKey = ref('${subTables[0].entityName?uncap_first}');
      <#list subTables as sub>
      <#if sub.foreignRelationType =='0'>
      <#assign hasOne2Many = true>
      const ${sub.entityName?uncap_first} = ref();
      </#if>
      <#if sub.foreignRelationType =='1'>
      <#assign hasOne2One = true>
      const ${sub.entityName?uncap_first}Form = ref();
      </#if>
      </#list>
      const tableRefs = {<#list subTables as sub><#if sub.foreignRelationType =='0'>${sub.entityName?uncap_first}, <#assign hasOne2Many = true></#if></#list>};
      <#list subTables as sub>
      <#if sub.foreignRelationType =='0'>
      const ${sub.entityName?uncap_first}Table = reactive({
        loading: false,
        dataSource: [],
        columns:${sub.entityName?uncap_first}Columns,
        show: false
      })
      </#if>
      </#list>

      const [handleChangeTabs,handleSubmit,requestSubTableData,formRef] = useJvxeMethod(requestAddOrEdit,classifyIntoFormData,tableRefs,activeKey,refKeys,validateSubForm);

      function classifyIntoFormData(allValues) {
        let main = Object.assign({}, allValues.formValue)
        return {
          ...main, // 展开
          <#assign subManyIndex = 0>
          <#list subTables as sub><#rt/>
          <#if sub.foreignRelationType =='0'>
          ${sub.entityName?uncap_first}List: allValues.tablesValue[${subManyIndex}].tableData,
          <#assign subManyIndex = subManyIndex+1>
          <#else>
          ${sub.entityName?uncap_first}List: ${sub.entityName?uncap_first}Form.value.getFormData(),
          </#if>
          </#list>
        }
      }
      <#if hasOne2One==true>
      //校验所有一对一子表表单
      function validateSubForm(allValues){
        return new Promise((resolve, _reject)=>{
          Promise.all([
            <#list subTables as sub><#rt/>
            <#if sub.foreignRelationType =='1'>
            ${sub.entityName?uncap_first}Form.value.validateForm(${sub_index}),
            </#if>
            </#list>
          ]).then(() => {
            resolve(allValues)
          }).catch(e => {
            if (e.error === VALIDATE_FAILED) {
              // 如果有未通过表单验证的子表，就自动跳转到它所在的tab
              activeKey.value = e.index == null ? unref(activeKey) : refKeys.value[e.index]
            } else {
              console.error(e)
            }
          })
        })
      }
      </#if>

      //表单提交事件
      async function requestAddOrEdit(values) {
        await saveOrUpdate(values, true);
      }

      const queryByIdUrl = '/${entityPackagePath}/${entityName?uncap_first}/queryById';
      async function initFormData(){
        let params = {id: props.formData.dataId};
        const data = await defHttp.get({url: queryByIdUrl, params});
        //设置表单的值
        await setFieldsValue({...data});
        <#list subTables as sub>
        <#if sub.foreignRelationType =='0'>
        requestSubTableData(${sub.entityName?uncap_first}List, {id: data.id}, ${sub.entityName?uncap_first}Table, ()=>{
          ${sub.entityName?uncap_first}Table.show = true;
        });
        </#if>
        <#if sub.foreignRelationType =='1'>
        ${sub.entityName?uncap_first}Form.value.initFormData(${sub.entityName?uncap_first}List, data.id);
        </#if>
        </#list>
        //默认是禁用
        await setProps({disabled: formDisabled.value})
      }

      initFormData();

      return {
        registerForm,
        formDisabled,
        formRef,
        handleSubmit,
        activeKey,
        handleChangeTabs,
        <#list subTables as sub>
        <#if sub.foreignRelationType =='0'>
        ${sub.entityName?uncap_first},
        </#if>
        <#if sub.foreignRelationType =='1'>
        ${sub.entityName?uncap_first}Form,
        </#if>
        </#list>
        <#list subTables as sub>
        <#if sub.foreignRelationType =='0'>
        ${sub.entityName?uncap_first}Table,
        </#if>
        </#list>
      }
    }
  });
</script>