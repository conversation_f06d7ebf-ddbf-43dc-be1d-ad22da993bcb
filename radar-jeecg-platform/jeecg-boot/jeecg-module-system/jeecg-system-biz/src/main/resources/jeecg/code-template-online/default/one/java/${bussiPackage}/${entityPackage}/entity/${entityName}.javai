<#include "/common/utils.ftl">
package ${bussiPackage}.${entityPackage}.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: ${tableVo.ftlDescription}
 * @Author: jeecg-boot
 * @Date:   ${.now?string["yyyy-MM-dd"]}
 * @Version: V1.0
 */
@Data
@TableName("${tableName}")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description="${tableVo.ftlDescription}")
public class ${entityName} implements Serializable {
    private static final long serialVersionUID = 1L;

    <#assign excel_ignore_arr=['createBy','createTime','updateBy','updateTime','sysOrgCode']>
    <#assign excel_ignore_classType_arr=['pca','switch','cat_tree']>
    <#list originalColumns as po>
    <#-- 生成字典Code -->
    <#assign list_field_dictCode="">
    <#if po.classType='sel_user'>
      <#assign list_field_dictCode=', dictTable = "sys_user", dicText = "${camelToDashed(po.extendParams.text?default(\"realname\")?trim)}", dicCode = "${camelToDashed(po.extendParams.store?default(\"username\")?trim)}"'>
    <#elseif po.classType='sel_depart'>
      <#assign list_field_dictCode=', dictTable = "sys_depart", dicText = "${camelToDashed(po.extendParams.text?default(\"depart_name\")?trim)}", dicCode = "${camelToDashed(po.extendParams.store?default(\"id\")?trim)}"'>
    <#elseif po.classType=='list' || po.classType=='list_multi' || po.classType=='sel_search' || po.classType=='radio' || po.classType=='checkbox'>
      <#if po.dictTable?default("")?trim?length gt 1>
        <#assign list_field_dictCode=', dictTable = "${po.dictTable}", dicText = "${po.dictText}", dicCode = "${po.dictField}"'>
      <#elseif po.dictField?default("")?trim?length gt 1>
        <#assign list_field_dictCode=', dicCode = "${po.dictField}"'>
      </#if>
    <#elseif po.classType=='sel_tree'>
        <#assign list_field_dictCode=', dictTable = "${po.dictTable}", dicText = "${po.dictText?split(",")[2]}", dicCode = "${po.dictText?split(",")[0]}"'>
    </#if>
	/**${po.filedComment}*/
	<#if po.fieldName == primaryKeyField>
	@TableId(type = IdType.ASSIGN_ID)
	<#else>
  		<#if po.fieldDbType =='Date' || po.fieldDbType =='Datetime'>
			<#if po.classType=='date'>
    <#if !excel_ignore_arr?seq_contains("${po.fieldName}")>
	@Excel(name = "${po.filedComment}", width = 15, format = "yyyy-MM-dd")
	</#if>
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
			<#else>
    <#if !excel_ignore_arr?seq_contains("${po.fieldName}")>
	@Excel(name = "${po.filedComment}", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	</#if>
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
			</#if>
		<#else>
    <#if !excel_ignore_arr?seq_contains("${po.fieldName}") && !excel_ignore_classType_arr?seq_contains("${po.classType}")>
	@Excel(name = "${po.filedComment}", width = 15${list_field_dictCode})
	</#if>
  		</#if>
      <#if list_field_dictCode?length gt 1>
	@Dict(${list_field_dictCode?substring(2)})
      </#if>
  		<#--  <#if po.classType!='popup'>
  			<#if po.dictTable?default("")?trim?length gt 1>
  	@Dict(dicCode="${po.dictField}",dicText="${po.dictText}",dictTable="${po.dictTable}")
  			<#elseif po.dictField?default("")?trim?length gt 1>
  	@Dict(dicCode="${po.dictField}")
  			</#if>
  		</#if>-->
    </#if>
    <#include "/common/blob.ftl">
	</#list>
}
