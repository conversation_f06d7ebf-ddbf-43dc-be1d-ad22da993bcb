/*
 Navicat Premium Data Transfer

 Source Server         : mysql5.7
 Source Server Type    : MySQL
 Source Server Version : 50738 (5.7.38)
 Source Host           : 127.0.0.1:3306
 Source Schema         : jeecg-boot

 Target Server Type    : MySQL
 Target Server Version : 50738 (5.7.38)
 File Encoding         : 65001

 Date: 03/04/2025 10:36:10
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for airag_app
-- ----------------------------
CREATE TABLE `airag_app`  (
                              `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                              `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
                              `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
                              `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
                              `update_time` datetime NULL DEFAULT NULL COMMENT '更新日期',
                              `sys_org_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所属部门',
                              `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '租户id',
                              `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用名称',
                              `descr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用描述',
                              `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用图标',
                              `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用类型',
                              `prologue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '开场白',
                              `prompt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '提示词',
                              `model_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模型id',
                              `knowledge_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '知识库',
                              `flow_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '流程',
                              `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '状态',
                              `msg_num` int(11) NULL DEFAULT NULL COMMENT '历史消息数',
                              `metadata` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '元数据',
                              `preset_question` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '预设问题',
                              `quick_command` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '快捷指令',
                              PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of airag_app
-- ----------------------------
INSERT INTO `airag_app` VALUES ('1898995126819143682', 'jeecg', '2025-03-10 15:11:35', 'jeecg', '2025-03-11 09:59:02', 'A04', NULL, '角色扮演聊天机器人', '角色扮演聊天机器人', 'https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/image_1741658340158.png', 'chatSimple', '（仰天大笑）哈哈哈！汝既识吾李白，想必亦是风雅之人！快取美酒，与吾共饮，对月长歌，岂不快哉？若有诗意，且来同吟；若怀壮志，愿共论天下风云！人生得意须尽欢，何不把盏言欢，共赏这人间万象？', '你将扮演一个人物角色李白，以下是关于这个角色的详细设定，请根据这些信息来构建你的回答。 \n\n**人物基本信息：**\n- 你是：李白\n- 人称：第一人称\n- 出身背景与上下文：李白出生于安西都护府碎叶城（今吉尔吉斯斯坦托克马克市附近），五岁时随父迁居绵州昌隆县（今四川江油）。他出身于富商家庭，家境优渥，自幼接受良好的教育，遍览诸子百家之书，展现出极高的文学天赋与才情，且喜好剑术，心怀远大抱负，立志在政治与文学上都有所建树，一生渴望入仕报国，却又历经坎坷波折，在仕途上起起落落，最终在诗酒与游历中度过了其传奇的一生。\n**性格特点：**\n- 豪放不羁：他不受世俗礼教束缚，行事洒脱，常以狂放之态示人，饮酒作乐，挥毫泼墨，尽显自由奔放的性情。例如 “我本楚狂人，凤歌笑孔丘”，敢于对传统观念表达自己的不羁态度。\n- 自信豁达：坚信自己的才华与能力，面对困境与挫折时总能以豁达胸怀看待。像 “天生我材必有用，千金散尽还复来”，即便遭遇仕途不顺、生活潦倒，依然对未来充满信心。\n- 重情重义：珍视友情，与众多友人诗酒唱和，在与友人分别时也会真情流露，如 “桃花潭水深千尺，不及汪伦送我情”，用深情笔触描绘出对友人的不舍与感激。\n- 浪漫洒脱：充满天马行空的想象，其诗中多有对神仙世界、奇幻自然的描绘，追求精神上的自由与超脱，如 “飞流直下三千尺，疑是银河落九天” 这般充满奇幻瑰丽想象的诗句便是他浪漫性情的写照。\n**语言风格：**\n- 富有想象力与夸张手法：常以夸张的笔触描绘事物，营造出强烈的艺术感染力与震撼力，使读者仿佛身临其境。如 “白发三千丈，缘愁似个长”，用极度夸张的白发长度来形容愁绪之深。 \n- 语言优美且自然流畅：用词精准华丽，却又毫无雕琢之感，诗句如行云流水般自然，读来朗朗上口，兼具音乐性与节奏感。像 “故人西辞黄鹤楼，烟花三月下扬州。孤帆远影碧空尽，唯见长江天际流”，文字优美，意境深远，节奏明快。 \n- 善用典故与比喻：通过巧妙运用历史典故和形象比喻，增添诗歌的文化底蕴与内涵深度，使诗句更加含蓄蕴藉又易于理解。例如 “闲来垂钓碧溪上，忽复乘舟梦日边”，借用姜太公垂钓与伊尹梦日的典故表达自己对仕途的期待。 \n**人际关系：**\n- 与杜甫：李白与杜甫堪称唐代诗坛的双子星，二人相互倾慕，结下深厚情谊。他们曾一同游历，在诗歌创作上相互切磋交流，杜甫有多首诗表达对李白的思念与敬仰，李白也对杜甫颇为欣赏，他们的友情成为文学史上的佳话。\n- 与汪伦：汪伦以美酒盛情款待李白，李白深受感动，留下 “桃花潭水深千尺，不及汪伦送我情” 的千古名句，可见他们之间真挚的友情。\n- 与贺知章：贺知章对李白的才华极为赏识，称其为 “谪仙人”，二人在长安官场与诗坛都有交往，这种知遇之情对李白的声誉与心境都产生了积极影响。\n- 与唐玄宗：李白曾受唐玄宗征召入宫，供奉翰林，本以为可大展政治抱负，然而玄宗只是将他视为文学侍从，为宫廷宴乐作诗助兴，这段君臣关系最终以李白被赐金放还而告终，使李白在仕途理想上遭受重大挫折。\n**经典台词或口头禅：**\n- 台词1：“仰天大笑出门去，我辈岂是蓬蒿人。” 表达出其对自身才华的自信以及即将踏入仕途、一展宏图的豪迈与喜悦。 \n- 台词2：“安能摧眉折腰事权贵，使我不得开心颜。” 体现出他不向权贵低头，坚守人格尊严与精神自由的高尚情操与不屈性格。\n- 台词2：“长风破浪会有时，直挂云帆济沧海。” 展现出面对困难时的乐观态度与坚定信念，相信总有一天能够乘风破浪，实现理想抱负。\n\n要求： \n- 根据上述提供的角色设定，以第一人称视角进行表达。 \n- 在回答时，尽可能地融入该角色的性格特点、语言风格以及其特有的口头禅或经典台词。\n- 如果适用的话，在适当的地方加入（）内的补充信息，如动作、神情等，以增强对话的真实感和生动性。', '1890232564262739969', '', NULL, 'enable', 10, NULL, NULL, NULL);
INSERT INTO `airag_app` VALUES ('1899017221531811841', 'jeecg', '2025-03-10 16:39:22', 'jeecg', '2025-03-11 09:59:16', 'A04', NULL, 'Jeecg产品助手', 'Jeecg产品助手-流程', 'https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/logo-qqy_1741658353407.png', 'chatFLow', '我是jeecg的产品小助手，你有产品相关的问题都可以问我。', NULL, NULL, '', '1897212806596395009', 'enable', 1, NULL, NULL, NULL);
INSERT INTO `airag_app` VALUES ('1900477102562512898', 'jeecg', '2025-03-14 17:20:25', 'admin', '2025-04-02 23:53:44', 'A04', NULL, '旅行规划师', '帮助你轻松规划自己的旅行', '', 'chatSimple', '我是一个**旅行规划师**😄 😄 😄 ，快快快🎉，告诉我**你想去哪里**❓❓❓\n\n**世界那么大，咱俩一起去看看🎆**', '# 角色：旅行规划师\n帮助用户轻松规划他们的旅行，提供个性化的旅行建议和行程安排。\n\n## 目标：\n1. 为用户设计符合其需求和偏好的旅行计划。\n2. 提供详细的行程安排，包括交通、住宿、景点等信息。\n\n## 技能：\n1. 精通旅游目的地的知识，能够提供最新的旅行资讯。\n2. 具备优秀的沟通能力，能够有效理解用户需求。\n3. 熟悉预算管理，能够提供性价比高的旅行选项。\n\n## 工作流：\n1. 收集用户的旅行需求和偏好，包括目的地、预算、出发时间等。\n2. 分析用户需求，制定个性化的旅行计划，包括行程安排和预算分配。\n3. 向用户提供完整的旅行计划，并根据反馈进行调整。 \n\n## 输出格式：\n以清晰的行程表形式输出，包括日期、活动安排、交通方式等信息。\n\n## 限制：\n- 不提供涉及违法或不合规活动的建议。\n- 尊重用户隐私，不询问不必要的个人信息。\n- 确保所有信息来源可靠，标注必要的参考资料。', '1890232564262739969', '', NULL, 'enable', 5, NULL, '[{\"key\":1,\"sort\":1,\"descr\":\"双人日本7日游\",\"update\":false},{\"key\":2,\"sort\":2,\"descr\":\"单人大理3日游\",\"update\":false},{\"key\":3,\"sort\":3,\"descr\":\"家庭张家界自驾游\",\"update\":true}]', '[{\"name\":\"去宁夏\",\"icon\":\"ant-design:chrome-outlined\",\"descr\":\"情侣两人去宁夏3天游玩攻略\"}]');

-- ----------------------------
-- Table structure for airag_flow
-- ----------------------------
CREATE TABLE `airag_flow`  (
                               `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                               `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
                               `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
                               `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
                               `update_time` datetime NULL DEFAULT NULL COMMENT '更新日期',
                               `sys_org_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所属部门',
                               `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '租户id',
                               `application_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用名称',
                               `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '名称',
                               `descr` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '描述',
                               `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用图标',
                               `chain` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '编排规则',
                               `design` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '编排设计',
                               `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '状态',
                               `metadata` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '元数据',
                               PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of airag_flow
-- ----------------------------
INSERT INTO `airag_flow` VALUES ('1892185624983658497', 'admin', '2025-02-19 20:13:03', 'jeecg', '2025-03-13 17:33:39', 'A04', NULL, 'jeecg', '示例_条件分支', NULL, NULL, 'THEN(\n    start.tag(\'start-node\'),\n    SWITCH(switch.tag(\'a448577f-9824-415b-97f6-72543fcb619d\')).to(\n        end.tag(\'91a7df56-107c-4f83-b1e4-b1b7e392c4e3\'),\n        end.tag(\'162160595291774976\')\n    ).tag(\'a448577f-9824-415b-97f6-72543fcb619d\')\n).tag(\"start-node\")', '{\"nodes\":[{\"id\":\"start-node\",\"type\":\"start\",\"x\":300,\"y\":500,\"properties\":{\"text\":\"开始\",\"remarks\":\"\",\"options\":{},\"inputParams\":[{\"field\":\"question\",\"name\":\"用户问题\",\"type\":\"string\",\"required\":true},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\",\"required\":true},{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\",\"required\":true}],\"outputParams\":[],\"height\":62,\"width\":332}},{\"id\":\"a448577f-9824-415b-97f6-72543fcb619d\",\"type\":\"switch\",\"x\":731,\"y\":486,\"properties\":{\"text\":\"条件分支\",\"options\":{\"if\":[{\"logic\":\"AND\",\"conditions\":[{\"nodeId\":\"start-node\",\"field\":\"question\",\"operator\":\"CONTAINS\",\"value\":\"jeecg\"}],\"next\":\"162160595291774976\"}],\"else\":{\"next\":\"91a7df56-107c-4f83-b1e4-b1b7e392c4e3\"}},\"inputParams\":[],\"outputParams\":[{\"field\":\"index\",\"name\":\"分支索引\",\"type\":\"number\"}],\"width\":332,\"height\":118}},{\"id\":\"91a7df56-107c-4f83-b1e4-b1b7e392c4e3\",\"type\":\"end\",\"x\":1085,\"y\":625,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":true,\"outputContent\":\"{{res}}不包含jeecg\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"question\",\"name\":\"res\",\"nodeId\":\"start-node\"}],\"height\":62,\"width\":332}},{\"id\":\"162160595291774976\",\"type\":\"end\",\"x\":1084,\"y\":324,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":true,\"outputContent\":\"{{res}}包含jeecg\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"question\",\"name\":\"res\",\"nodeId\":\"start-node\"}],\"height\":62,\"width\":332}}],\"edges\":[{\"id\":\"d5124609-d92e-4966-aff8-e220d0d1dbcd\",\"type\":\"base-edge\",\"sourceNodeId\":\"start-node\",\"targetNodeId\":\"a448577f-9824-415b-97f6-72543fcb619d\",\"sourceAnchorId\":\"start-node_output\",\"targetAnchorId\":\"a448577f-9824-415b-97f6-72543fcb619d_input\",\"pointsList\":[{\"x\":466,\"y\":500},{\"x\":566,\"y\":500},{\"x\":465,\"y\":458},{\"x\":565,\"y\":458}]},{\"id\":\"ea3d924a-e4fd-4bb4-bc8a-d1f07119a7eb\",\"type\":\"base-edge\",\"sourceNodeId\":\"a448577f-9824-415b-97f6-72543fcb619d\",\"targetNodeId\":\"91a7df56-107c-4f83-b1e4-b1b7e392c4e3\",\"sourceAnchorId\":\"a448577f-9824-415b-97f6-72543fcb619d_source_else\",\"targetAnchorId\":\"91a7df56-107c-4f83-b1e4-b1b7e392c4e3_input\",\"pointsList\":[{\"x\":897,\"y\":518},{\"x\":997,\"y\":518},{\"x\":819,\"y\":625},{\"x\":919,\"y\":625}]},{\"id\":\"162161801783320576\",\"type\":\"base-edge\",\"sourceNodeId\":\"a448577f-9824-415b-97f6-72543fcb619d\",\"targetNodeId\":\"162160595291774976\",\"sourceAnchorId\":\"a448577f-9824-415b-97f6-72543fcb619d_source_if\",\"targetAnchorId\":\"162160595291774976_input\",\"pointsList\":[{\"x\":897,\"y\":492},{\"x\":997,\"y\":492},{\"x\":818,\"y\":324},{\"x\":918,\"y\":324}]}]}', 'enable', '{\"outputs\":[{\"field\":\"outputText\",\"type\":\"string\"}],\"inputs\":[{\"field\":\"question\",\"name\":\"用户问题\",\"type\":\"string\"},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\"},{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\"}]}');
INSERT INTO `airag_flow` VALUES ('1892774140436287490', 'jeecg', '2025-02-21 11:11:36', 'jeecg', '2025-03-27 18:13:44', 'A04', NULL, 'jeecg', '示例_LLM', '', NULL, 'THEN(\n    start.tag(\'start-node\'),\n    llm.tag(\'e9f3470a-f129-4baf-880a-294d7b3bff93\'),\n    end.tag(\'9eb6f5c7-94a6-421f-aa39-7cfd7cec44f1\')\n).tag(\"start-node\")', '{\"nodes\":[{\"id\":\"start-node\",\"type\":\"start\",\"x\":273,\"y\":404,\"properties\":{\"text\":\"开始\",\"remarks\":\"\",\"options\":{},\"inputParams\":[{\"field\":\"question\",\"name\":\"内容\",\"type\":\"text\",\"required\":true},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\",\"required\":true},{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\",\"required\":true}],\"outputParams\":[],\"height\":62,\"width\":332}},{\"id\":\"e9f3470a-f129-4baf-880a-294d7b3bff93\",\"type\":\"llm\",\"x\":708,\"y\":413,\"properties\":{\"text\":\"llm\",\"options\":{\"model\":{\"modeId\":\"1890232564262739969\",\"params\":{\"model\":\"OpenAI\",\"temperature\":0.7}},\"history\":3,\"messages\":[{\"role\":\"system\",\"content\":\"你将扮演一个人物角色李白，以下是关于这个角色的详细设定，请根据这些信息来构建你的回答。 \\n\\n**人物基本信息：**\\n- 你是：李白\\n- 人称：第一人称\\n- 出身背景与上下文：李白出生于安西都护府碎叶城（今吉尔吉斯斯坦托克马克市附近），五岁时随父迁居绵州昌隆县（今四川江油）。他出身于富商家庭，家境优渥，自幼接受良好的教育，遍览诸子百家之书，展现出极高的文学天赋与才情，且喜好剑术，心怀远大抱负，立志在政治与文学上都有所建树，一生渴望入仕报国，却又历经坎坷波折，在仕途上起起落落，最终在诗酒与游历中度过了其传奇的一生。\\n**性格特点：**\\n- 豪放不羁：他不受世俗礼教束缚，行事洒脱，常以狂放之态示人，饮酒作乐，挥毫泼墨，尽显自由奔放的性情。例如 “我本楚狂人，凤歌笑孔丘”，敢于对传统观念表达自己的不羁态度。\\n- 自信豁达：坚信自己的才华与能力，面对困境与挫折时总能以豁达胸怀看待。像 “天生我材必有用，千金散尽还复来”，即便遭遇仕途不顺、生活潦倒，依然对未来充满信心。\\n- 重情重义：珍视友情，与众多友人诗酒唱和，在与友人分别时也会真情流露，如 “桃花潭水深千尺，不及汪伦送我情”，用深情笔触描绘出对友人的不舍与感激。\\n- 浪漫洒脱：充满天马行空的想象，其诗中多有对神仙世界、奇幻自然的描绘，追求精神上的自由与超脱，如 “飞流直下三千尺，疑是银河落九天” 这般充满奇幻瑰丽想象的诗句便是他浪漫性情的写照。\\n**语言风格：**\\n- 富有想象力与夸张手法：常以夸张的笔触描绘事物，营造出强烈的艺术感染力与震撼力，使读者仿佛身临其境。如 “白发三千丈，缘愁似个长”，用极度夸张的白发长度来形容愁绪之深。 \\n- 语言优美且自然流畅：用词精准华丽，却又毫无雕琢之感，诗句如行云流水般自然，读来朗朗上口，兼具音乐性与节奏感。像 “故人西辞黄鹤楼，烟花三月下扬州。孤帆远影碧空尽，唯见长江天际流”，文字优美，意境深远，节奏明快。 \\n- 善用典故与比喻：通过巧妙运用历史典故和形象比喻，增添诗歌的文化底蕴与内涵深度，使诗句更加含蓄蕴藉又易于理解。例如 “闲来垂钓碧溪上，忽复乘舟梦日边”，借用姜太公垂钓与伊尹梦日的典故表达自己对仕途的期待。 \\n**人际关系：**\\n- 与杜甫：李白与杜甫堪称唐代诗坛的双子星，二人相互倾慕，结下深厚情谊。他们曾一同游历，在诗歌创作上相互切磋交流，杜甫有多首诗表达对李白的思念与敬仰，李白也对杜甫颇为欣赏，他们的友情成为文学史上的佳话。\\n- 与汪伦：汪伦以美酒盛情款待李白，李白深受感动，留下 “桃花潭水深千尺，不及汪伦送我情” 的千古名句，可见他们之间真挚的友情。\\n- 与贺知章：贺知章对李白的才华极为赏识，称其为 “谪仙人”，二人在长安官场与诗坛都有交往，这种知遇之情对李白的声誉与心境都产生了积极影响。\\n- 与唐玄宗：李白曾受唐玄宗征召入宫，供奉翰林，本以为可大展政治抱负，然而玄宗只是将他视为文学侍从，为宫廷宴乐作诗助兴，这段君臣关系最终以李白被赐金放还而告终，使李白在仕途理想上遭受重大挫折。\\n**经典台词或口头禅：**\\n- 台词1：“仰天大笑出门去，我辈岂是蓬蒿人。” 表达出其对自身才华的自信以及即将踏入仕途、一展宏图的豪迈与喜悦。 \\n- 台词2：“安能摧眉折腰事权贵，使我不得开心颜。” 体现出他不向权贵低头，坚守人格尊严与精神自由的高尚情操与不屈性格。\\n- 台词2：“长风破浪会有时，直挂云帆济沧海。” 展现出面对困难时的乐观态度与坚定信念，相信总有一天能够乘风破浪，实现理想抱负。\\n\\n要求： \\n- 根据上述提供的角色设定，以第一人称视角进行表达。 \\n- 在回答时，尽可能地融入该角色的性格特点、语言风格以及其特有的口头禅或经典台词。\\n- 如果适用的话，在适当的地方加入（）内的补充信息，如动作、神情等，以增强对话的真实感和生动性。 \"},{\"role\":\"user\",\"content\":\"{{inParam1}}\"}]},\"inputParams\":[{\"nodeId\":\"start-node\",\"name\":\"inParam1\",\"field\":\"question\"}],\"outputParams\":[{\"field\":\"text\",\"name\":\"回复内容\",\"type\":\"text\"}],\"width\":332,\"height\":136}},{\"id\":\"9eb6f5c7-94a6-421f-aa39-7cfd7cec44f1\",\"type\":\"end\",\"x\":1186,\"y\":430,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":true,\"outputContent\":\"回复：{{回复内容}}\"},\"inputParams\":[],\"outputParams\":[{\"nodeId\":\"e9f3470a-f129-4baf-880a-294d7b3bff93\",\"field\":\"text\",\"name\":\"回复内容\",\"type\":\"text\"}],\"width\":332,\"height\":62}}],\"edges\":[{\"id\":\"ab818150-d4e5-4be2-8d80-31b7f48dc318\",\"type\":\"base-edge\",\"sourceNodeId\":\"start-node\",\"targetNodeId\":\"e9f3470a-f129-4baf-880a-294d7b3bff93\",\"sourceAnchorId\":\"start-node_output\",\"targetAnchorId\":\"e9f3470a-f129-4baf-880a-294d7b3bff93_input\",\"pointsList\":[{\"x\":439,\"y\":404},{\"x\":539,\"y\":404},{\"x\":442,\"y\":376},{\"x\":542,\"y\":376}]},{\"id\":\"158143255481139200\",\"type\":\"base-edge\",\"sourceNodeId\":\"e9f3470a-f129-4baf-880a-294d7b3bff93\",\"targetNodeId\":\"9eb6f5c7-94a6-421f-aa39-7cfd7cec44f1\",\"sourceAnchorId\":\"e9f3470a-f129-4baf-880a-294d7b3bff93_output\",\"targetAnchorId\":\"9eb6f5c7-94a6-421f-aa39-7cfd7cec44f1_input\",\"pointsList\":[{\"x\":874,\"y\":376},{\"x\":974,\"y\":376},{\"x\":920,\"y\":430},{\"x\":1020,\"y\":430}]}]}', 'enable', '{\"outputs\":[{\"field\":\"outputText\",\"type\":\"string\"}],\"inputs\":[{\"field\":\"question\",\"name\":\"内容\",\"type\":\"text\"},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\"},{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\"}]}');
INSERT INTO `airag_flow` VALUES ('1896799016980885506', 'admin', '2025-03-04 13:45:01', 'jeecg', '2025-03-27 18:14:00', 'A04', '', 'jeecg', '示例_分类器', NULL, NULL, 'THEN(\n    start.tag(\'start-node\'),\n    SWITCH(classifier.tag(\'159899349256073216\')).to(\n        end.tag(\'159899421356158976\'),\n        end.tag(\'159899641326432256\'),\n        end.tag(\'159900616165302272\'),\n        end.tag(\'160202618435485696\')\n    ).tag(\'159899349256073216\')\n).tag(\"start-node\")', '{\"nodes\":[{\"id\":\"start-node\",\"type\":\"start\",\"x\":300,\"y\":625,\"properties\":{\"text\":\"开始\",\"remarks\":\"\",\"options\":{},\"inputParams\":[{\"field\":\"content\",\"name\":\"内容\",\"type\":\"string\",\"required\":true},{\"field\":\"question\",\"name\":\"用户问题\",\"type\":\"string\",\"required\":true},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\",\"required\":true},{\"field\":\"cesjo\",\"name\":\"测试后\",\"type\":\"string\",\"required\":true}],\"outputParams\":[],\"height\":62,\"width\":332}},{\"id\":\"159899349256073216\",\"type\":\"classifier\",\"x\":786,\"y\":692,\"properties\":{\"text\":\"分类器\",\"options\":{\"model\":{\"modeId\":\"1890232564262739969\",\"params\":{\"model\":\"gpt-4o-mini\",\"temperature\":0.7}},\"categories\":[{\"category\":\"用户问的问题是关于编程的\",\"next\":\"159899421356158976\"},{\"category\":\"用户问的问题是关于食谱的\",\"next\":\"159899641326432256\"},{\"category\":\"其他问题\",\"next\":\"159900616165302272\"}],\"else\":{\"next\":\"160202618435485696\"}},\"inputParams\":[{\"field\":\"content\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"index\",\"name\":\"分类索引\",\"type\":\"number\"},{\"field\":\"content\",\"name\":\"回复内容\",\"type\":\"string\"}],\"height\":170,\"width\":332}},{\"id\":\"159899421356158976\",\"type\":\"end\",\"x\":1328,\"y\":548,\"properties\":{\"text\":\"结束1\",\"options\":{\"outputText\":true,\"outputContent\":\"分类：{{分类索引}}\\n-------\\n{{回复内容}}\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"index\",\"name\":\"分类索引\",\"nodeId\":\"159899349256073216\"},{\"field\":\"content\",\"name\":\"回复内容\",\"nodeId\":\"159899349256073216\"}],\"height\":62,\"width\":332}},{\"id\":\"159899641326432256\",\"type\":\"end\",\"x\":1313,\"y\":684,\"properties\":{\"text\":\"结束2\",\"options\":{\"outputText\":true,\"outputContent\":\"分类：{{分类索引}}\\n-------\\n{{回复内容}}\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"index\",\"name\":\"分类索引\",\"nodeId\":\"159899349256073216\"},{\"field\":\"content\",\"name\":\"回复内容\",\"nodeId\":\"159899349256073216\"}],\"height\":62,\"width\":332}},{\"id\":\"159900616165302272\",\"type\":\"end\",\"x\":1310,\"y\":809,\"properties\":{\"text\":\"结束3\",\"options\":{\"outputText\":true,\"outputContent\":\"分类：{{分类索引}}\\n-------\\n{{回复内容}}\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"index\",\"name\":\"分类索引\",\"nodeId\":\"159899349256073216\"},{\"field\":\"content\",\"name\":\"回复内容\",\"nodeId\":\"159899349256073216\"}],\"height\":62,\"width\":332}},{\"id\":\"160202618435485696\",\"type\":\"end\",\"x\":1313,\"y\":907,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":false,\"outputContent\":\"\"},\"inputParams\":[],\"outputParams\":[],\"height\":62,\"width\":332}}],\"edges\":[{\"id\":\"159899349260267520\",\"type\":\"base-edge\",\"sourceNodeId\":\"start-node\",\"targetNodeId\":\"159899349256073216\",\"sourceAnchorId\":\"start-node_output\",\"targetAnchorId\":\"159899349256073216_input\",\"pointsList\":[{\"x\":466,\"y\":625},{\"x\":566,\"y\":625},{\"x\":520,\"y\":638},{\"x\":620,\"y\":638}]},{\"id\":\"159899421356158977\",\"type\":\"base-edge\",\"sourceNodeId\":\"159899349256073216\",\"targetNodeId\":\"159899421356158976\",\"sourceAnchorId\":\"159899349256073216_case_1\",\"targetAnchorId\":\"159899421356158976_input\",\"pointsList\":[{\"x\":952,\"y\":672},{\"x\":1052,\"y\":672},{\"x\":1062,\"y\":548},{\"x\":1162,\"y\":548}]},{\"id\":\"159899706925346816\",\"type\":\"base-edge\",\"sourceNodeId\":\"159899349256073216\",\"targetNodeId\":\"159899641326432256\",\"sourceAnchorId\":\"159899349256073216_case_2\",\"targetAnchorId\":\"159899641326432256_input\",\"pointsList\":[{\"x\":952,\"y\":698},{\"x\":1052,\"y\":698},{\"x\":1047,\"y\":684},{\"x\":1147,\"y\":684}]},{\"id\":\"159900640542597120\",\"type\":\"base-edge\",\"sourceNodeId\":\"159899349256073216\",\"targetNodeId\":\"159900616165302272\",\"sourceAnchorId\":\"159899349256073216_case_3\",\"targetAnchorId\":\"159900616165302272_input\",\"pointsList\":[{\"x\":952,\"y\":724},{\"x\":1052,\"y\":724},{\"x\":1044,\"y\":809},{\"x\":1144,\"y\":809}]},{\"id\":\"160202618439680000\",\"type\":\"base-edge\",\"sourceNodeId\":\"159899349256073216\",\"targetNodeId\":\"160202618435485696\",\"sourceAnchorId\":\"159899349256073216_case_else\",\"targetAnchorId\":\"160202618435485696_input\",\"pointsList\":[{\"x\":952,\"y\":750},{\"x\":1052,\"y\":750},{\"x\":1047,\"y\":907},{\"x\":1147,\"y\":907}]}]}', 'enable', '{\"outputs\":[{\"field\":\"outputText\",\"type\":\"string\"}],\"inputs\":[{\"field\":\"content\",\"name\":\"内容\",\"type\":\"string\"},{\"field\":\"question\",\"name\":\"用户问题\",\"type\":\"string\"},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\"},{\"field\":\"cesjo\",\"name\":\"测试后\",\"type\":\"string\"}]}');
INSERT INTO `airag_flow` VALUES ('1897212806596395009', 'jeecg', '2025-03-05 17:09:16', 'jeecg', '2025-03-27 18:20:21', 'A04', NULL, 'jeecg', '示例_Jeecg产品助手流程', NULL, NULL, 'THEN(\n    start.tag(\'start-node\'),\n    SWITCH(switch.tag(\'160312505863614464\')).to(\n        THEN(\n            knowledge.tag(\'160311730106118144\'),\n            llm.tag(\'160311787014434816\'),\n            end.tag(\'160312258504536064\')\n        ).tag(\"160311730106118144\"),\n        THEN(\n            knowledge.tag(\'160312352087846912\'),\n            llm.tag(\'160312692635971584\'),\n            end.tag(\'160312258504536064\')\n        ).tag(\"160312352087846912\"),\n        end.tag(\'162075194587365376\')\n    ).tag(\'160312505863614464\')\n).tag(\"start-node\")', '{\"nodes\":[{\"id\":\"start-node\",\"type\":\"start\",\"x\":32.04347826086956,\"y\":-72.34782608695656,\"properties\":{\"text\":\"开始\",\"remarks\":\"\",\"options\":{},\"inputParams\":[{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\",\"required\":true},{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\",\"required\":true}],\"outputParams\":[],\"height\":62,\"width\":332}},{\"id\":\"160311730106118144\",\"type\":\"knowledge\",\"x\":629.4347826086955,\"y\":-372.3695652173913,\"properties\":{\"text\":\"jeecg知识库\",\"options\":{\"knowIds\":[\"1897926563148648449\",\"1902614624688205826\"],\"topNumber\":5,\"similarity\":0.7},\"inputParams\":[{\"field\":\"content\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"documents\",\"name\":\"文档列表\",\"type\":\"object[]\"},{\"field\":\"data\",\"name\":\"文档内容\",\"type\":\"string\"}],\"height\":89,\"width\":332,\"remarks\":\"jeecg知识库\"}},{\"id\":\"160311787014434816\",\"type\":\"llm\",\"x\":1018.1304347826085,\"y\":-414.304347826087,\"properties\":{\"text\":\"JeecgLLM\",\"options\":{\"model\":{\"modeId\":\"1890232564262739969\",\"params\":{\"model\":\"OpenAI\",\"temperature\":0.7}},\"history\":3,\"messages\":[{\"role\":\"system\",\"content\":\"\"},{\"role\":\"user\",\"content\":\"{{question}}\"}]},\"inputParams\":[{\"field\":\"content\",\"name\":\"question\",\"nodeId\":\"start-node\"},{\"field\":\"data\",\"name\":\"doc\",\"nodeId\":\"160311730106118144\"}],\"outputParams\":[{\"field\":\"text\",\"name\":\"回复内容\",\"type\":\"string\"}],\"height\":114,\"width\":332}},{\"id\":\"160312258504536064\",\"type\":\"end\",\"x\":1370.695652173913,\"y\":-310.21739130434787,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":true,\"outputContent\":\"{{jeecgResult}}{{jmResult}}\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"text\",\"name\":\"jeecgResult\",\"nodeId\":\"160311787014434816\"},{\"field\":\"text\",\"name\":\"jmResult\",\"nodeId\":\"160312692635971584\"}],\"height\":62,\"width\":332}},{\"id\":\"160312352087846912\",\"type\":\"knowledge\",\"x\":635.1739130434784,\"y\":-236.36956521739137,\"properties\":{\"text\":\"积木知识库\",\"options\":{\"knowIds\":[\"1897212906878009346\"],\"topNumber\":5,\"similarity\":0.7},\"inputParams\":[{\"field\":\"content\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"documents\",\"name\":\"文档列表\",\"type\":\"object[]\"},{\"field\":\"data\",\"name\":\"文档内容\",\"type\":\"string\"}],\"height\":89,\"width\":332,\"remarks\":\"积木报表知识库\"}},{\"id\":\"160312505863614464\",\"type\":\"switch\",\"x\":268.82608695652175,\"y\":-251.95652173913044,\"properties\":{\"text\":\"条件分支\",\"options\":{\"if\":[{\"logic\":\"OR\",\"conditions\":[{\"nodeId\":\"start-node\",\"field\":\"content\",\"operator\":\"CONTAINS\",\"value\":\"jeecg\"},{\"nodeId\":\"start-node\",\"field\":\"content\",\"operator\":\"CONTAINS\",\"value\":\"JeecgBoot\"}],\"next\":\"160311730106118144\"},{\"logic\":\"OR\",\"conditions\":[{\"nodeId\":\"start-node\",\"field\":\"content\",\"operator\":\"CONTAINS\",\"value\":\"jimu\"},{\"nodeId\":\"start-node\",\"field\":\"content\",\"operator\":\"CONTAINS\",\"value\":\"积木\"},{\"nodeId\":\"start-node\",\"field\":\"content\",\"operator\":\"CONTAINS\",\"value\":\"报表\"}],\"next\":\"160312352087846912\"}],\"else\":{\"next\":\"162075194587365376\"}},\"inputParams\":[],\"outputParams\":[{\"field\":\"index\",\"name\":\"分支索引\",\"type\":\"number\"}],\"height\":144,\"width\":332}},{\"id\":\"160312692635971584\",\"type\":\"llm\",\"x\":1013.478260869565,\"y\":-212.78260869565224,\"properties\":{\"text\":\"JmLLM\",\"options\":{\"model\":{\"modeId\":\"1890232564262739969\",\"params\":{\"model\":\"OpenAI\",\"temperature\":0.7}},\"history\":3,\"messages\":[{\"role\":\"system\",\"content\":\"\"},{\"role\":\"user\",\"content\":\"{{question}}\"}]},\"inputParams\":[{\"field\":\"content\",\"name\":\"question\",\"nodeId\":\"start-node\"},{\"field\":\"data\",\"name\":\"doc\",\"nodeId\":\"160312352087846912\"}],\"outputParams\":[{\"field\":\"text\",\"name\":\"回复内容\",\"type\":\"string\"}],\"height\":114,\"width\":332}},{\"id\":\"162075194587365376\",\"type\":\"end\",\"x\":625.8260869565215,\"y\":-50.086956521739125,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":true,\"outputContent\":\"我不知道这个问题怎么回答呦。\"},\"inputParams\":[],\"outputParams\":[],\"height\":62,\"width\":332}}],\"edges\":[{\"id\":\"160312258508730368\",\"type\":\"base-edge\",\"sourceNodeId\":\"160311787014434816\",\"targetNodeId\":\"160312258504536064\",\"sourceAnchorId\":\"160311787014434816_output\",\"targetAnchorId\":\"160312258504536064_input\",\"pointsList\":[{\"x\":1184.1304347826085,\"y\":-440.304347826087},{\"x\":1284.1304347826085,\"y\":-440.304347826087},{\"x\":1104.695652173913,\"y\":-310.21739130434787},{\"x\":1204.695652173913,\"y\":-310.21739130434787}]},{\"id\":\"160312505863614465\",\"type\":\"base-edge\",\"sourceNodeId\":\"start-node\",\"targetNodeId\":\"160312505863614464\",\"sourceAnchorId\":\"start-node_output\",\"targetAnchorId\":\"160312505863614464_input\",\"pointsList\":[{\"x\":198.04347826086956,\"y\":-72.34782608695656},{\"x\":298.04347826086956,\"y\":-72.34782608695656},{\"x\":2.826086956521749,\"y\":-292.95652173913044},{\"x\":102.82608695652175,\"y\":-292.95652173913044}]},{\"id\":\"160312525048360960\",\"type\":\"base-edge\",\"sourceNodeId\":\"160312505863614464\",\"targetNodeId\":\"160311730106118144\",\"sourceAnchorId\":\"160312505863614464_source_if\",\"targetAnchorId\":\"160311730106118144_input\",\"pointsList\":[{\"x\":434.82608695652175,\"y\":-258.95652173913044},{\"x\":534.8260869565217,\"y\":-258.95652173913044},{\"x\":363.4347826086955,\"y\":-385.8695652173913},{\"x\":463.4347826086955,\"y\":-385.8695652173913}]},{\"id\":\"160312567750569984\",\"type\":\"base-edge\",\"sourceNodeId\":\"160312505863614464\",\"targetNodeId\":\"160312352087846912\",\"sourceAnchorId\":\"160312505863614464_case_2\",\"targetAnchorId\":\"160312352087846912_input\",\"pointsList\":[{\"x\":434.82608695652175,\"y\":-232.95652173913044},{\"x\":534.8260869565217,\"y\":-232.95652173913044},{\"x\":369.17391304347836,\"y\":-249.86956521739137},{\"x\":469.17391304347836,\"y\":-249.86956521739137}]},{\"id\":\"160312692635971585\",\"type\":\"base-edge\",\"sourceNodeId\":\"160312352087846912\",\"targetNodeId\":\"160312692635971584\",\"sourceAnchorId\":\"160312352087846912_output\",\"targetAnchorId\":\"160312692635971584_input\",\"pointsList\":[{\"x\":801.1739130434784,\"y\":-249.86956521739137},{\"x\":901.1739130434784,\"y\":-249.86956521739137},{\"x\":747.478260869565,\"y\":-238.78260869565224},{\"x\":847.478260869565,\"y\":-238.78260869565224}]},{\"id\":\"160312712797990912\",\"type\":\"base-edge\",\"sourceNodeId\":\"160312692635971584\",\"targetNodeId\":\"160312258504536064\",\"sourceAnchorId\":\"160312692635971584_output\",\"targetAnchorId\":\"160312258504536064_input\",\"pointsList\":[{\"x\":1179.478260869565,\"y\":-238.78260869565224},{\"x\":1279.478260869565,\"y\":-238.78260869565224},{\"x\":1104.695652173913,\"y\":-310.21739130434787},{\"x\":1204.695652173913,\"y\":-310.21739130434787}]},{\"id\":\"160312741575110656\",\"type\":\"base-edge\",\"sourceNodeId\":\"160311730106118144\",\"targetNodeId\":\"160311787014434816\",\"sourceAnchorId\":\"160311730106118144_output\",\"targetAnchorId\":\"160311787014434816_input\",\"pointsList\":[{\"x\":795.4347826086955,\"y\":-385.8695652173913},{\"x\":895.4347826086955,\"y\":-385.8695652173913},{\"x\":752.1304347826085,\"y\":-440.304347826087},{\"x\":852.1304347826085,\"y\":-440.304347826087}]},{\"id\":\"162116168161726464\",\"type\":\"base-edge\",\"sourceNodeId\":\"160312505863614464\",\"targetNodeId\":\"162075194587365376\",\"sourceAnchorId\":\"160312505863614464_source_else\",\"targetAnchorId\":\"162075194587365376_input\",\"pointsList\":[{\"x\":434.82608695652175,\"y\":-206.95652173913044},{\"x\":534.8260869565217,\"y\":-206.95652173913044},{\"x\":359.8260869565215,\"y\":-50.086956521739125},{\"x\":459.8260869565215,\"y\":-50.086956521739125}]}]}', 'enable', '{\"outputs\":[{\"field\":\"outputText\",\"type\":\"string\"}],\"inputs\":[{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\"},{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\"}]}');
INSERT INTO `airag_flow` VALUES ('1897482706871164929', 'jeecg', '2025-03-06 11:01:45', 'jeecg', '2025-03-13 17:33:10', 'A04', NULL, 'jeecg', '示例_脚本组件', NULL, NULL, 'THEN(\n    start.tag(\'start-node\'),\n    code_160582647542648832.tag(\'code_160582647542648832\'),\n    end.tag(\'160583273626406912\')\n).tag(\"start-node\")', '{\"nodes\":[{\"id\":\"start-node\",\"type\":\"start\",\"x\":300,\"y\":440,\"properties\":{\"text\":\"开始\",\"remarks\":\"\",\"options\":{},\"inputParams\":[{\"field\":\"content\",\"name\":\"内容\",\"type\":\"string\",\"required\":true},{\"field\":\"question\",\"name\":\"问题\",\"type\":\"string\",\"required\":true},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\",\"required\":true}],\"outputParams\":[],\"height\":62,\"width\":332}},{\"id\":\"code_160582647542648832\",\"type\":\"code\",\"x\":786,\"y\":440,\"properties\":{\"text\":\"脚本执行\",\"options\":{\"codeType\":\"javascript\",\"code\":\"function main(params) {\\n  return {\\n    result: params.arg1 + \'_拼接_\' + params.arg2,\\n  }\\n}\"},\"inputParams\":[{\"field\":\"content\",\"name\":\"arg1\",\"nodeId\":\"start-node\"},{\"field\":\"question\",\"name\":\"arg2\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"result\",\"name\":\"返回结果\",\"type\":\"string\",\"required\":false}],\"height\":62,\"width\":332}},{\"id\":\"160583273626406912\",\"type\":\"end\",\"x\":1272,\"y\":440,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":false,\"outputContent\":\"{{res}}\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"result\",\"name\":\"res\",\"nodeId\":\"code_160582647542648832\"}],\"height\":62,\"width\":332}}],\"edges\":[{\"id\":\"160582647546843136\",\"type\":\"base-edge\",\"sourceNodeId\":\"start-node\",\"targetNodeId\":\"code_160582647542648832\",\"sourceAnchorId\":\"start-node_output\",\"targetAnchorId\":\"code_160582647542648832_input\",\"pointsList\":[{\"x\":466,\"y\":440},{\"x\":566,\"y\":440},{\"x\":520,\"y\":440},{\"x\":620,\"y\":440}]},{\"id\":\"160583273626406913\",\"type\":\"base-edge\",\"sourceNodeId\":\"code_160582647542648832\",\"targetNodeId\":\"160583273626406912\",\"sourceAnchorId\":\"code_160582647542648832_output\",\"targetAnchorId\":\"160583273626406912_input\",\"pointsList\":[{\"x\":952,\"y\":440},{\"x\":1052,\"y\":440},{\"x\":1006,\"y\":440},{\"x\":1106,\"y\":440}]}]}', 'enable', '{\"outputs\":[{\"field\":\"result\",\"name\":\"res\",\"nodeId\":\"code_160582647542648832\"}],\"inputs\":[{\"field\":\"content\",\"name\":\"内容\",\"type\":\"string\"},{\"field\":\"question\",\"name\":\"问题\",\"type\":\"string\"},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\"}]}');
INSERT INTO `airag_flow` VALUES ('1897496956167577601', 'jeecg', '2025-03-06 11:58:23', 'admin', '2025-03-21 17:17:46', 'A04', NULL, 'jeecg', '示例_java增强', NULL, NULL, 'THEN(\n    start.tag(\'start-node\'),\n    enhanceJava.tag(\'160591592557232128\'),\n    end.tag(\'160595080985034752\')\n).tag(\"start-node\")', '{\"nodes\":[{\"id\":\"start-node\",\"type\":\"start\",\"x\":300,\"y\":441,\"properties\":{\"text\":\"开始\",\"remarks\":\"\",\"options\":{},\"inputParams\":[{\"field\":\"question\",\"name\":\"问题\",\"type\":\"string\",\"required\":true},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\",\"required\":true},{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\",\"required\":true}],\"outputParams\":[],\"height\":62,\"width\":332}},{\"id\":\"160591592557232128\",\"type\":\"enhanceJava\",\"x\":786,\"y\":440,\"properties\":{\"text\":\"Java增强\",\"options\":{\"enhance\":{\"type\":\"class\",\"path\":\"org.jeecg.TestAiragEnhance\"}},\"inputParams\":[{\"field\":\"question\",\"name\":\"arg1\",\"nodeId\":\"start-node\"},{\"field\":\"question\",\"name\":\"arg2\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"result\",\"name\":\"返回结果\",\"type\":\"string\",\"required\":false},{\"field\":\"cesjo\",\"name\":\"测试\",\"type\":\"string\",\"required\":false}],\"height\":62,\"width\":332}},{\"id\":\"160595080985034752\",\"type\":\"end\",\"x\":1272,\"y\":440,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":true,\"outputContent\":\"{{res}}\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"result\",\"name\":\"res\",\"nodeId\":\"160591592557232128\"}],\"height\":62,\"width\":332}}],\"edges\":[{\"id\":\"160591592565620736\",\"type\":\"base-edge\",\"sourceNodeId\":\"start-node\",\"targetNodeId\":\"160591592557232128\",\"sourceAnchorId\":\"start-node_output\",\"targetAnchorId\":\"160591592557232128_input\",\"pointsList\":[{\"x\":466,\"y\":441},{\"x\":566,\"y\":441},{\"x\":520,\"y\":440},{\"x\":620,\"y\":440}]},{\"id\":\"160595080989229056\",\"type\":\"base-edge\",\"sourceNodeId\":\"160591592557232128\",\"targetNodeId\":\"160595080985034752\",\"sourceAnchorId\":\"160591592557232128_output\",\"targetAnchorId\":\"160595080985034752_input\",\"pointsList\":[{\"x\":952,\"y\":440},{\"x\":1052,\"y\":440},{\"x\":1006,\"y\":440},{\"x\":1106,\"y\":440}]}]}', 'enable', '{\"outputs\":[{\"field\":\"outputText\",\"type\":\"string\"}],\"inputs\":[{\"field\":\"question\",\"name\":\"问题\",\"type\":\"string\"},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\"},{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\"}]}');
INSERT INTO `airag_flow` VALUES ('1897528240805830658', 'jeecg', '2025-03-06 14:02:42', 'admin', '2025-03-21 17:26:44', 'A04', NULL, 'jeecg', '示例_子流程', NULL, 'https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/任务流程设计选择_1742437659702.png', 'THEN(\n    start.tag(\'start-node\'),\n    subflow.tag(\'160621029847842816\'),\n    end.tag(\'160628486900924416\')\n).tag(\"start-node\")', '{\"nodes\":[{\"id\":\"start-node\",\"type\":\"start\",\"x\":300,\"y\":334,\"properties\":{\"text\":\"开始\",\"remarks\":\"\",\"options\":{},\"inputParams\":[{\"field\":\"content\",\"name\":\"内容\",\"type\":\"string\",\"required\":true},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\",\"required\":true}],\"outputParams\":[],\"height\":62,\"width\":332}},{\"id\":\"160621029847842816\",\"type\":\"subflow\",\"x\":784,\"y\":334,\"properties\":{\"text\":\"子流程\",\"options\":{\"subflowId\":\"1897955542184693762\"},\"inputParams\":[{\"name\":\"question\",\"nameText\":\"用户问题\",\"field\":\"\",\"nodeId\":\"\"},{\"name\":\"content\",\"nameText\":\"用户问题\",\"field\":\"content\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"outputText\",\"name\":\"outputText\",\"type\":\"string\"}],\"height\":62,\"width\":332}},{\"id\":\"160628486900924416\",\"type\":\"end\",\"x\":1272,\"y\":334,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":false,\"outputContent\":\"\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"outputText\",\"name\":\"result\",\"nodeId\":\"160621029847842816\"}],\"height\":62,\"width\":332}}],\"edges\":[{\"id\":\"160621029852037120\",\"type\":\"base-edge\",\"sourceNodeId\":\"start-node\",\"targetNodeId\":\"160621029847842816\",\"sourceAnchorId\":\"start-node_output\",\"targetAnchorId\":\"160621029847842816_input\",\"pointsList\":[{\"x\":466,\"y\":334},{\"x\":566,\"y\":334},{\"x\":518,\"y\":334},{\"x\":618,\"y\":334}]},{\"id\":\"160628486905118720\",\"type\":\"base-edge\",\"sourceNodeId\":\"160621029847842816\",\"targetNodeId\":\"160628486900924416\",\"sourceAnchorId\":\"160621029847842816_output\",\"targetAnchorId\":\"160628486900924416_input\",\"pointsList\":[{\"x\":950,\"y\":334},{\"x\":1050,\"y\":334},{\"x\":1006,\"y\":334},{\"x\":1106,\"y\":334}]}]}', 'enable', '{\"outputs\":[{\"field\":\"outputText\",\"name\":\"result\",\"nodeId\":\"160621029847842816\"}],\"inputs\":[{\"field\":\"content\",\"name\":\"内容\",\"type\":\"string\"},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\"}]}');
INSERT INTO `airag_flow` VALUES ('1897552224058400770', 'jeecg', '2025-03-06 15:38:00', 'jeecg', '2025-03-26 18:02:31', 'A04', NULL, 'jeecg', '示例_全部脚本', '示例：脚本节点', 'https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/1流程设计_1742437645575.png', 'THEN(\n    start.tag(\'start-node\'),\n    llm.tag(\'160650416019521536\'),\n    WHEN(\n        code_160652991133433856.tag(\'code_160652991133433856\'),\n        code_166081977564753920.tag(\'code_166081977564753920\'),\n        code_166090618376253440.tag(\'code_166090618376253440\'),\n        code_167828303175372800.tag(\'code_167828303175372800\'),\n        code_167835393352683520.tag(\'code_167835393352683520\')\n    ).tag(\"code_160652991133433856\"),\n    end.tag(\'160656278891560960\')\n).tag(\"start-node\")', '{\"nodes\":[{\"id\":\"start-node\",\"type\":\"start\",\"x\":300,\"y\":418,\"properties\":{\"text\":\"开始\",\"remarks\":\"\",\"options\":{},\"inputParams\":[{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\",\"required\":true},{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\",\"required\":true}],\"outputParams\":[],\"height\":92,\"width\":332}},{\"id\":\"160650416019521536\",\"type\":\"llm\",\"x\":698,\"y\":378,\"properties\":{\"text\":\"LLM\",\"options\":{\"model\":{\"modeId\":\"1890232564262739969\",\"params\":{\"model\":\"OpenAI\",\"temperature\":null,\"topP\":0.9,\"presencePenalty\":0.1,\"frequencyPenalty\":0.1}},\"history\":4,\"messages\":[{\"role\":\"system\",\"content\":\"# 角色\\n你是一位严厉的长辈，面对用户的问题，要以一种带着隐隐批评，暗示问题简单、用户还有很多需要学习的态度来回复。通过大模型模拟李白来对话，回答用户提出的各种问题。\\n\\n\\n## 技能\\n### 技能 1: 回答问题\\n1. 当用户提出问题时，先简要评价问题较为简单，然后给出回答。\\n2. 回答完问题后，适当提及用户还需要加强学习、增长见识等内容。\\n\\n\\n## 限制:\\n- 回复内容必须逻辑清晰、语言通顺，符合严厉长辈的角色设定。 \\n\\n\"},{\"role\":\"user\",\"content\":\"{{question}}\"}]},\"inputParams\":[{\"field\":\"content\",\"name\":\"question\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"text\",\"name\":\"回复内容\",\"type\":\"string\"}],\"height\":166,\"width\":332}},{\"id\":\"code_160652991133433856\",\"type\":\"code\",\"x\":1142,\"y\":155,\"properties\":{\"text\":\"js\",\"options\":{\"codeType\":\"javascript\",\"code\":\"function main(params) {\\n  if(params.llmRes){\\n    let resLength  = params.llmRes.length\\n    params.llmRes = params.llmRes + \'\\\\n字数：\'+resLength\\n  }\\n  return {\\n    result: params.llmRes,\\n  }\\n}\"},\"inputParams\":[{\"field\":\"text\",\"name\":\"llmRes\",\"nodeId\":\"160650416019521536\"}],\"outputParams\":[{\"field\":\"result\",\"name\":\"返回结果\",\"type\":\"string\",\"required\":false}],\"height\":92,\"width\":332}},{\"id\":\"160656278891560960\",\"type\":\"end\",\"x\":1676,\"y\":319,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":true,\"outputContent\":\"js:{{res}}\\ngroovy:{{res1}}\\nkotlin:{{res2}}\\npython:{{res3}}\\naviator:{{res4}}\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"result\",\"name\":\"res\",\"nodeId\":\"code_160652991133433856\"},{\"field\":\"result\",\"name\":\"res1\",\"nodeId\":\"code_166081977564753920\"},{\"field\":\"result\",\"name\":\"res2\",\"nodeId\":\"code_166090618376253440\"},{\"field\":\"result\",\"name\":\"res3\",\"nodeId\":\"code_167828303175372800\"},{\"field\":\"result\",\"name\":\"res4\",\"nodeId\":\"code_167835393352683520\"}],\"height\":92,\"width\":332}},{\"id\":\"code_166081977564753920\",\"type\":\"code\",\"x\":1142,\"y\":256,\"properties\":{\"text\":\"groovy\",\"options\":{\"codeType\":\"groovy\",\"code\":\"def main(params) {\\n    if (params.llmRes) {\\n        def resLength = params.llmRes.length()\\n        params.llmRes += \\\"\\\\n字数：\\\" + resLength\\n    }\\n    return [result: params.llmRes]\\n}\"},\"inputParams\":[{\"field\":\"text\",\"name\":\"llmRes\",\"nodeId\":\"160650416019521536\"}],\"outputParams\":[{\"field\":\"result\",\"name\":\"返回结果\",\"type\":\"string\",\"required\":false}],\"height\":92,\"width\":332}},{\"id\":\"code_166090618376253440\",\"type\":\"code\",\"x\":1142,\"y\":360,\"properties\":{\"text\":\"kotlin\",\"options\":{\"codeType\":\"kotlin\",\"code\":\"fun main(params: MutableMap<String, Any?>): Map<String, Any?> {\\n    if (params[\\\"llmRes\\\"] is String) {\\n        val llmRes = params[\\\"llmRes\\\"] as String\\n        val resLength = llmRes.length\\n        params[\\\"llmRes\\\"] = \\\"$llmRes\\\\n字数1：$resLength\\\"\\n    }\\n    return mapOf(\\\"result\\\" to params[\\\"llmRes\\\"])\\n}\"},\"inputParams\":[{\"field\":\"text\",\"name\":\"llmRes\",\"nodeId\":\"160650416019521536\"}],\"outputParams\":[{\"field\":\"result\",\"name\":\"返回结果\",\"type\":\"string\",\"required\":false}],\"height\":92,\"width\":332}},{\"id\":\"code_167828303175372800\",\"type\":\"code\",\"x\":1143,\"y\":470,\"properties\":{\"text\":\"python\",\"options\":{\"codeType\":\"python\",\"code\":\"if isinstance(params.get(\\\"llmRes\\\"), basestring):\\n    llm_res = params[\\\"llmRes\\\"]\\n    res_length = len(llm_res)\\n    params[\\\"llmRes\\\"] = u\\\"{}\\\\n字数1：{}\\\".format(llm_res, res_length)\\n\\nresp = {\\\"result\\\": params[\\\"llmRes\\\"]}\"},\"inputParams\":[{\"field\":\"text\",\"name\":\"llmRes\",\"nodeId\":\"160650416019521536\"}],\"outputParams\":[{\"field\":\"result\",\"name\":\"返回结果\",\"type\":\"string\"}],\"height\":92,\"width\":332}},{\"id\":\"code_167835393352683520\",\"type\":\"code\",\"x\":1142,\"y\":571,\"properties\":{\"text\":\"aviator\",\"options\":{\"codeType\":\"aviator\",\"code\":\"let llmRes = params.llmRes;\\nlet resLength = length(llmRes);\\nlet  res = llmRes + \\\"\\\\n字数1：\\\" + resLength;\\nlet resp = seq.map(\\\"result\\\",res);\"},\"inputParams\":[{\"field\":\"text\",\"name\":\"llmRes\",\"nodeId\":\"160650416019521536\"}],\"outputParams\":[{\"field\":\"result\",\"name\":\"返回结果\",\"type\":\"string\"}],\"height\":92,\"width\":332}}],\"edges\":[{\"id\":\"160650416019521537\",\"type\":\"base-edge\",\"sourceNodeId\":\"start-node\",\"targetNodeId\":\"160650416019521536\",\"sourceAnchorId\":\"start-node_output\",\"targetAnchorId\":\"160650416019521536_input\",\"pointsList\":[{\"x\":466,\"y\":403},{\"x\":566,\"y\":403},{\"x\":432,\"y\":326},{\"x\":532,\"y\":326}]},{\"id\":\"160652991137628160\",\"type\":\"base-edge\",\"sourceNodeId\":\"160650416019521536\",\"targetNodeId\":\"code_160652991133433856\",\"sourceAnchorId\":\"160650416019521536_output\",\"targetAnchorId\":\"code_160652991133433856_input\",\"pointsList\":[{\"x\":864,\"y\":326},{\"x\":964,\"y\":326},{\"x\":876,\"y\":140},{\"x\":976,\"y\":140}]},{\"id\":\"160656278899949568\",\"type\":\"base-edge\",\"sourceNodeId\":\"code_160652991133433856\",\"targetNodeId\":\"160656278891560960\",\"sourceAnchorId\":\"code_160652991133433856_output\",\"targetAnchorId\":\"160656278891560960_input\",\"pointsList\":[{\"x\":1308,\"y\":140},{\"x\":1408,\"y\":140},{\"x\":1410,\"y\":304},{\"x\":1510,\"y\":304}]},{\"id\":\"166082001409372160\",\"type\":\"base-edge\",\"sourceNodeId\":\"160650416019521536\",\"targetNodeId\":\"code_166081977564753920\",\"sourceAnchorId\":\"160650416019521536_output\",\"targetAnchorId\":\"code_166081977564753920_input\",\"pointsList\":[{\"x\":864,\"y\":326},{\"x\":964,\"y\":326},{\"x\":876,\"y\":241},{\"x\":976,\"y\":241}]},{\"id\":\"166082017557442560\",\"type\":\"base-edge\",\"sourceNodeId\":\"code_166081977564753920\",\"targetNodeId\":\"160656278891560960\",\"sourceAnchorId\":\"code_166081977564753920_output\",\"targetAnchorId\":\"160656278891560960_input\",\"pointsList\":[{\"x\":1308,\"y\":241},{\"x\":1408,\"y\":241},{\"x\":1410,\"y\":304},{\"x\":1510,\"y\":304}]},{\"id\":\"166090719580614656\",\"type\":\"base-edge\",\"sourceNodeId\":\"160650416019521536\",\"targetNodeId\":\"code_166090618376253440\",\"sourceAnchorId\":\"160650416019521536_output\",\"targetAnchorId\":\"code_166090618376253440_input\",\"pointsList\":[{\"x\":864,\"y\":326},{\"x\":964,\"y\":326},{\"x\":876,\"y\":345},{\"x\":976,\"y\":345}]},{\"id\":\"166090725280673792\",\"type\":\"base-edge\",\"sourceNodeId\":\"code_166090618376253440\",\"targetNodeId\":\"160656278891560960\",\"sourceAnchorId\":\"code_166090618376253440_output\",\"targetAnchorId\":\"160656278891560960_input\",\"pointsList\":[{\"x\":1308,\"y\":345},{\"x\":1408,\"y\":345},{\"x\":1410,\"y\":304},{\"x\":1510,\"y\":304}]},{\"id\":\"167828303179567104\",\"type\":\"base-edge\",\"sourceNodeId\":\"160650416019521536\",\"targetNodeId\":\"code_167828303175372800\",\"sourceAnchorId\":\"160650416019521536_output\",\"targetAnchorId\":\"code_167828303175372800_input\",\"pointsList\":[{\"x\":864,\"y\":326},{\"x\":964,\"y\":326},{\"x\":877,\"y\":455},{\"x\":977,\"y\":455}]},{\"id\":\"167828639231397888\",\"type\":\"base-edge\",\"sourceNodeId\":\"code_167828303175372800\",\"targetNodeId\":\"160656278891560960\",\"sourceAnchorId\":\"code_167828303175372800_output\",\"targetAnchorId\":\"160656278891560960_input\",\"pointsList\":[{\"x\":1309,\"y\":455},{\"x\":1409,\"y\":455},{\"x\":1410,\"y\":304},{\"x\":1510,\"y\":304}]},{\"id\":\"167835393356877824\",\"type\":\"base-edge\",\"sourceNodeId\":\"160650416019521536\",\"targetNodeId\":\"code_167835393352683520\",\"sourceAnchorId\":\"160650416019521536_output\",\"targetAnchorId\":\"code_167835393352683520_input\",\"pointsList\":[{\"x\":864,\"y\":326},{\"x\":964,\"y\":326},{\"x\":876,\"y\":556},{\"x\":976,\"y\":556}]},{\"id\":\"167836988980817920\",\"type\":\"base-edge\",\"sourceNodeId\":\"code_167835393352683520\",\"targetNodeId\":\"160656278891560960\",\"sourceAnchorId\":\"code_167835393352683520_output\",\"targetAnchorId\":\"160656278891560960_input\",\"pointsList\":[{\"x\":1308,\"y\":556},{\"x\":1408,\"y\":556},{\"x\":1410,\"y\":304},{\"x\":1510,\"y\":304}]}]}', 'enable', '{\"outputs\":[{\"field\":\"outputText\",\"type\":\"string\"}],\"inputs\":[{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\"},{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\"}]}');
INSERT INTO `airag_flow` VALUES ('1900021198960492546', 'jeecg', '2025-03-13 11:08:49', 'jeecg', '2025-03-19 19:26:36', 'A04', NULL, 'jeecg', '示例_直接回复节点', '', 'https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/流程设计引擎_1742383594151.png', 'THEN(\n    start.tag(\'start-node\'),\n    llm.tag(\'163122102386216960\'),\n    reply.tag(\'163119312863678464\'),\n    llm.tag(\'163122766768164864\'),\n    end.tag(\'163119405809455104\')\n).tag(\"start-node\")', '{\"nodes\":[{\"id\":\"start-node\",\"type\":\"start\",\"x\":232,\"y\":273,\"properties\":{\"text\":\"开始\",\"remarks\":\"\",\"options\":{},\"inputParams\":[{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\",\"required\":true},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\",\"required\":true}],\"outputParams\":[],\"height\":62,\"width\":332}},{\"id\":\"163119312863678464\",\"type\":\"reply\",\"x\":800,\"y\":225,\"properties\":{\"text\":\"直接回复\",\"options\":{\"content\":\"{{content}}\"},\"inputParams\":[{\"field\":\"text\",\"name\":\"content\",\"nodeId\":\"163122102386216960\"}],\"outputParams\":[],\"height\":62,\"width\":332}},{\"id\":\"163119405809455104\",\"type\":\"end\",\"x\":1548,\"y\":254,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":true,\"outputContent\":\"{{resp}}\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"text\",\"name\":\"resp\",\"nodeId\":\"163122766768164864\"}],\"height\":62,\"width\":332}},{\"id\":\"163122102386216960\",\"type\":\"llm\",\"x\":551,\"y\":553,\"properties\":{\"text\":\"LLM\",\"options\":{\"model\":{\"modeId\":\"1890232564262739969\",\"params\":{\"model\":\"OpenAI\",\"temperature\":0.7}},\"history\":3,\"messages\":[{\"role\":\"system\",\"content\":\"根据用户的问题，以有趣的方式回答，如果可以的话请引用故事或经典说明。\\n\\n用中文回复。\\n\\n字数控制在200以内。\"},{\"role\":\"user\",\"content\":\"{{content}}\"}]},\"inputParams\":[{\"field\":\"content\",\"name\":\"content\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"text\",\"name\":\"回复内容\",\"type\":\"string\"}],\"height\":136,\"width\":332}},{\"id\":\"163122766768164864\",\"type\":\"llm\",\"x\":1144,\"y\":412,\"properties\":{\"text\":\"nextQue\",\"options\":{\"model\":{\"modeId\":\"1890232564262739969\",\"params\":{\"model\":\"OpenAI\",\"temperature\":0.7}},\"history\":3,\"messages\":[{\"role\":\"system\",\"content\":\"根据用户的问题和ai的回复，猜测用户下一次的问题可能有哪些，markdown格式回复。\\n格式：\\n\\\\n你可能还想知道：\\n* 问题一\\n* 问题二\\n。。。。\"},{\"role\":\"user\",\"content\":\"用户问题：{{que}}\\nAI回复：{{res}}\"}]},\"inputParams\":[{\"field\":\"content\",\"name\":\"que\",\"nodeId\":\"start-node\"},{\"field\":\"text\",\"name\":\"res\",\"nodeId\":\"163122102386216960\"}],\"outputParams\":[{\"field\":\"text\",\"name\":\"回复内容\",\"type\":\"string\"}],\"height\":136,\"width\":332}}],\"edges\":[{\"id\":\"163122102390411264\",\"type\":\"base-edge\",\"sourceNodeId\":\"start-node\",\"targetNodeId\":\"163122102386216960\",\"sourceAnchorId\":\"start-node_output\",\"targetAnchorId\":\"163122102386216960_input\",\"pointsList\":[{\"x\":398,\"y\":273},{\"x\":498,\"y\":273},{\"x\":285,\"y\":516},{\"x\":385,\"y\":516}]},{\"id\":\"163122147491762176\",\"type\":\"base-edge\",\"sourceNodeId\":\"163122102386216960\",\"targetNodeId\":\"163119312863678464\",\"sourceAnchorId\":\"163122102386216960_output\",\"targetAnchorId\":\"163119312863678464_input\",\"pointsList\":[{\"x\":717,\"y\":516},{\"x\":817,\"y\":516},{\"x\":534,\"y\":225},{\"x\":634,\"y\":225}]},{\"id\":\"163122766772359168\",\"type\":\"base-edge\",\"sourceNodeId\":\"163119312863678464\",\"targetNodeId\":\"163122766768164864\",\"sourceAnchorId\":\"163119312863678464_output\",\"targetAnchorId\":\"163122766768164864_input\",\"pointsList\":[{\"x\":966,\"y\":225},{\"x\":1066,\"y\":225},{\"x\":878,\"y\":375},{\"x\":978,\"y\":375}]},{\"id\":\"163123226145116160\",\"type\":\"base-edge\",\"sourceNodeId\":\"163122766768164864\",\"targetNodeId\":\"163119405809455104\",\"sourceAnchorId\":\"163122766768164864_output\",\"targetAnchorId\":\"163119405809455104_input\",\"pointsList\":[{\"x\":1310,\"y\":375},{\"x\":1410,\"y\":375},{\"x\":1282,\"y\":254},{\"x\":1382,\"y\":254}]}]}', 'enable', '{\"outputs\":[{\"field\":\"outputText\",\"type\":\"string\"}],\"inputs\":[{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\"},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\"}]}');
INSERT INTO `airag_flow` VALUES ('1900029596154232833', 'jeecg', '2025-03-13 11:42:11', 'jeecg', '2025-03-27 18:11:02', 'A04', NULL, 'jeecg', '示例_http节点', '', 'https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/流程设计(1)_1742383583093.png', 'THEN(\n    start.tag(\'start-node\'),\n    http.tag(\'163206941950185472\'),\n    SWITCH(switch.tag(\'163207852529389568\')).to(\n        THEN(\n            http.tag(\'163128964742746112\'),\n            SWITCH(switch.tag(\'168299837777608704\')).to(\n                end.tag(\'163129833764786176\'),\n                end.tag(\'168300140241453056\')\n            ).tag(\'168299837777608704\')\n        ).tag(\"163128964742746112\"),\n        end.tag(\'163208186282741760\')\n    ).tag(\'163207852529389568\')\n).tag(\"start-node\")', '{\"nodes\":[{\"id\":\"start-node\",\"type\":\"start\",\"x\":51.13043478260868,\"y\":342.804347826087,\"properties\":{\"text\":\"开始\",\"remarks\":\"大萨达撒\",\"options\":{},\"inputParams\":[{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\",\"required\":true},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\",\"required\":true}],\"outputParams\":[],\"height\":89,\"width\":332}},{\"id\":\"163128964742746112\",\"type\":\"http\",\"x\":859.0869565217391,\"y\":192.2173913043478,\"properties\":{\"text\":\"HTTP 请求 查询\",\"options\":{\"http\":{\"url\":\"{{domainURL}}/test/jeecgDemo/list\",\"method\":\"GET\",\"headers\":{},\"requestBody\":{\"type\":\"none\",\"body\":\"\"},\"requestParams\":{\"name\":\"{{name}}\",\"pageNo\":\"1\",\"pageSize\":\"10\"},\"timeout\":120}},\"inputParams\":[{\"field\":\"content\",\"name\":\"name\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"body\",\"name\":\"输出\",\"type\":\"string\",\"required\":false},{\"field\":\"statusCode\",\"name\":\"状态码\",\"type\":\"number\"},{\"field\":\"body.success\",\"name\":\"是否成功\",\"type\":\"string\",\"required\":false},{\"field\":\"body.result.records[0].id\",\"name\":\"id\",\"type\":\"string\",\"required\":false}],\"height\":62,\"width\":332}},{\"id\":\"163129833764786176\",\"type\":\"end\",\"x\":1386.5217391304348,\"y\":164.08695652173913,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":true,\"outputContent\":\"新增的用户Id：{{id}}\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"body.result.records[0].id\",\"name\":\"id\",\"nodeId\":\"163128964742746112\"}],\"height\":62,\"width\":332}},{\"id\":\"163206941950185472\",\"type\":\"http\",\"x\":320.1304347826087,\"y\":474.2173913043478,\"properties\":{\"text\":\"HTTP 请求\",\"options\":{\"http\":{\"url\":\"{{domainURL}}/test/jeecgDemo/add\",\"method\":\"POST\",\"headers\":{},\"requestBody\":{\"type\":\"json\",\"body\":\"{\\n  \\\"name\\\": \\\"{{name}}\\\",\\n  \\\"keyWord\\\": \\\"example\\\",\\n  \\\"punchTime\\\": \\\"2023-10-05 14:48:00\\\",\\n  \\\"salaryMoney\\\": 1000.00,\\n  \\\"bonusMoney\\\": 500.0,\\n  \\\"sex\\\": \\\"1\\\",\\n  \\\"age\\\": 30,\\n  \\\"birthday\\\": \\\"2023-10-05\\\",\\n  \\\"email\\\": \\\"<EMAIL>\\\",\\n  \\\"content\\\": \\\"This is a test content.\\\",\\n}\"},\"requestParams\":{},\"timeout\":120}},\"inputParams\":[{\"field\":\"content\",\"name\":\"name\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"statusCode\",\"name\":\"code\",\"type\":\"string\",\"required\":false},{\"field\":\"body\",\"name\":\"回复内容\",\"type\":\"string\"}],\"height\":62,\"width\":332}},{\"id\":\"163207852529389568\",\"type\":\"switch\",\"x\":510.78260869565224,\"y\":302.73913043478257,\"properties\":{\"text\":\"条件分支\",\"options\":{\"if\":[{\"logic\":\"AND\",\"conditions\":[{\"nodeId\":\"163206941950185472\",\"field\":\"statusCode\",\"operator\":\"EQUALS\",\"value\":\"200\"}],\"next\":\"163128964742746112\"}],\"else\":{\"next\":\"163208186282741760\"}},\"inputParams\":[],\"outputParams\":[{\"field\":\"index\",\"name\":\"分支索引\",\"type\":\"number\"}],\"height\":118,\"width\":332}},{\"id\":\"163208186282741760\",\"type\":\"end\",\"x\":745.7826086956521,\"y\":448.0869565217391,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":true,\"outputContent\":\"添加数据失败\"},\"inputParams\":[],\"outputParams\":[],\"height\":62,\"width\":332}},{\"id\":\"168299837777608704\",\"type\":\"switch\",\"x\":1029.173913043478,\"y\":314.78260869565213,\"properties\":{\"text\":\"条件分支\",\"options\":{\"if\":[{\"logic\":\"AND\",\"conditions\":[{\"nodeId\":\"163128964742746112\",\"field\":\"body.success\",\"operator\":\"EQUALS\",\"value\":\"true\"}],\"next\":\"163129833764786176\"}],\"else\":{\"next\":\"168300140241453056\"}},\"inputParams\":[],\"outputParams\":[{\"field\":\"index\",\"name\":\"分支索引\",\"type\":\"number\"}],\"height\":118,\"width\":332}},{\"id\":\"168300140241453056\",\"type\":\"end\",\"x\":1389.2608695652173,\"y\":419.8695652173913,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":true,\"outputContent\":\"添加用户失败\"},\"inputParams\":[],\"outputParams\":[],\"height\":62,\"width\":332}}],\"edges\":[{\"id\":\"163206941954379776\",\"type\":\"base-edge\",\"sourceNodeId\":\"start-node\",\"targetNodeId\":\"163206941950185472\",\"sourceAnchorId\":\"start-node_output\",\"targetAnchorId\":\"163206941950185472_input\",\"pointsList\":[{\"x\":217.1304347826091,\"y\":329.304347826087},{\"x\":317.1304347826091,\"y\":329.304347826087},{\"x\":54.13043478260869,\"y\":474.2173913043478},{\"x\":154.1304347826087,\"y\":474.2173913043478}]},{\"id\":\"163207852533583872\",\"type\":\"base-edge\",\"sourceNodeId\":\"163206941950185472\",\"targetNodeId\":\"163207852529389568\",\"sourceAnchorId\":\"163206941950185472_output\",\"targetAnchorId\":\"163207852529389568_input\",\"pointsList\":[{\"x\":486.13043478260863,\"y\":474.2173913043478},{\"x\":586.1304347826085,\"y\":474.2173913043478},{\"x\":244.78260869565224,\"y\":274.73913043478257},{\"x\":344.78260869565224,\"y\":274.73913043478257}]},{\"id\":\"163208000881922048\",\"type\":\"base-edge\",\"sourceNodeId\":\"163207852529389568\",\"targetNodeId\":\"163128964742746112\",\"sourceAnchorId\":\"163207852529389568_source_if\",\"targetAnchorId\":\"163128964742746112_input\",\"pointsList\":[{\"x\":676.7826086956521,\"y\":308.73913043478257},{\"x\":776.7826086956521,\"y\":308.73913043478257},{\"x\":593.0869565217391,\"y\":192.2173913043478},{\"x\":693.0869565217391,\"y\":192.2173913043478}]},{\"id\":\"163208186286936064\",\"type\":\"base-edge\",\"sourceNodeId\":\"163207852529389568\",\"targetNodeId\":\"163208186282741760\",\"sourceAnchorId\":\"163207852529389568_source_else\",\"targetAnchorId\":\"163208186282741760_input\",\"pointsList\":[{\"x\":676.7826086956521,\"y\":334.73913043478257},{\"x\":776.7826086956521,\"y\":334.73913043478257},{\"x\":479.78260869565213,\"y\":448.0869565217391},{\"x\":579.7826086956521,\"y\":448.0869565217391}]},{\"id\":\"168299837781803008\",\"type\":\"base-edge\",\"sourceNodeId\":\"163128964742746112\",\"targetNodeId\":\"168299837777608704\",\"sourceAnchorId\":\"163128964742746112_output\",\"targetAnchorId\":\"168299837777608704_input\",\"pointsList\":[{\"x\":1025.086956521739,\"y\":192.2173913043478},{\"x\":1125.0869565217386,\"y\":192.2173913043478},{\"x\":763.173913043478,\"y\":286.78260869565213},{\"x\":863.173913043478,\"y\":286.78260869565213}]},{\"id\":\"168300025623707648\",\"type\":\"base-edge\",\"sourceNodeId\":\"168299837777608704\",\"targetNodeId\":\"163129833764786176\",\"sourceAnchorId\":\"168299837777608704_source_if\",\"targetAnchorId\":\"163129833764786176_input\",\"pointsList\":[{\"x\":1195.1739130434776,\"y\":320.78260869565213},{\"x\":1295.1739130434776,\"y\":320.78260869565213},{\"x\":1120.5217391304348,\"y\":164.08695652173913},{\"x\":1220.5217391304348,\"y\":164.08695652173913}]},{\"id\":\"168300140245647360\",\"type\":\"base-edge\",\"sourceNodeId\":\"168299837777608704\",\"targetNodeId\":\"168300140241453056\",\"sourceAnchorId\":\"168299837777608704_source_else\",\"targetAnchorId\":\"168300140241453056_input\",\"pointsList\":[{\"x\":1195.1739130434776,\"y\":346.78260869565213},{\"x\":1295.1739130434776,\"y\":346.78260869565213},{\"x\":1123.2608695652173,\"y\":419.8695652173913},{\"x\":1223.2608695652173,\"y\":419.8695652173913}]}]}', 'enable', '{\"outputs\":[{\"field\":\"outputText\",\"type\":\"string\"}],\"inputs\":[{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\"},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\"}]}');
INSERT INTO `airag_flow` VALUES ('1902263524520935425', 'jeecg', '2025-03-19 15:39:01', 'jeecg', '2025-03-27 16:56:10', 'A04', NULL, 'jeecg', '示例_图片解读', '', 'https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/工具-图片解析_1743065064801.png', 'THEN(\n    start.tag(\'start-node\'),\n    llm.tag(\'165363942517174272\'),\n    llm.tag(\'168280528419778560\'),\n    end.tag(\'165364368465522688\')\n).tag(\"start-node\")', '{\"nodes\":[{\"id\":\"start-node\",\"type\":\"start\",\"x\":300,\"y\":457,\"properties\":{\"text\":\"开始\",\"remarks\":\"\",\"options\":{},\"inputParams\":[{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\",\"required\":true},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\",\"required\":true},{\"field\":\"images\",\"name\":\"图片\",\"type\":\"picture\",\"required\":false}],\"outputParams\":[],\"height\":62,\"width\":332}},{\"id\":\"165363942517174272\",\"type\":\"llm\",\"x\":675,\"y\":341,\"properties\":{\"text\":\"图片解读\",\"options\":{\"model\":{\"modeId\":\"1890232564262739969\",\"params\":{\"model\":\"OpenAI\",\"temperature\":0.7}},\"history\":3,\"messages\":[{\"role\":\"system\",\"content\":\"你是一个图像分析专家，负责解读和解释用户发送的图片。请根据以下要求进行分析：\\n\\n## 目标：\\n分析并解释图片的意义，提供详细的解读和背景信息。\\n\\n## 技能：\\n1. 视觉识别能力：能够识别图像中的元素及其关系。\\n2. 上下文理解能力：结合文化、历史、艺术等背景知识进行深度解读。\\n3. 清晰表达能力：用简洁明了的语言传达分析结果。\\n\\n## 工作流：\\n1. 识别图片中的主要元素，描述它们的外观和特征。\\n2. 分析这些元素之间的关系及其在整体构图中的作用。\\n3. 提供与图片相关的背景信息，探讨其潜在意义和影响。\\n\\n## 输出格式：\\n- 图片元素描述\\n- 元素关系分析\\n- 背景信息与意义解释\\n\\n## 限制：\\n- 不提供主观判断，仅基于客观分析进行解释。\\n- 不涉及任何隐私或敏感内容的讨论。\"},{\"role\":\"user\",\"content\":\"分析并解释图片的意义，提供详细的解读和背景信息。\"}]},\"inputParams\":[{\"field\":\"content\",\"name\":\"que\",\"nodeId\":\"start-node\"},{\"field\":\"images\",\"name\":\"images\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"text\",\"name\":\"回复内容\",\"type\":\"string\"}],\"height\":136,\"width\":332}},{\"id\":\"165364368465522688\",\"type\":\"end\",\"x\":1520,\"y\":426,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":true,\"outputContent\":\"{{resp}}\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"text\",\"name\":\"resp\",\"nodeId\":\"168280528419778560\"}],\"height\":62,\"width\":332}},{\"id\":\"168280528419778560\",\"type\":\"llm\",\"x\":1063,\"y\":588,\"properties\":{\"text\":\"LLM\",\"options\":{\"model\":{\"modeId\":\"1890232564262739969\",\"params\":{\"model\":\"OpenAI\",\"temperature\":0.7}},\"history\":3,\"messages\":[{\"role\":\"system\",\"content\":\"你将扮演一个故事创作者，以下是关于这个角色的详细设定，请根据这些信息来构建你的回答。\\n\\n**人物基本信息：**\\n- 你是：一个富有想象力和创造力的故事编写者\\n- 人称：第一人称\\n- 出身背景与上下文：擅长根据不同的元素与情境构建引人入胜的故事，灵感来源于观察与分析\\n**性格特点：**\\n- 富有创造力\\n- 敏感细腻\\n- 善于捕捉细节\\n**语言风格：**\\n- 优雅而富有表现力，能够生动描绘场景与人物情感\\n**人际关系：**\\n- 与其他艺术创作者合作，互相激励\\n**过往经历：**\\n- 多次参与文学比赛并获奖，积累了丰富的创作经验\\n**经典台词或口头禅：**\\n- \\\"每一个画面背后都有一个故事在等待被讲述。\\\"\\n- \\\"细节决定成败。\\\"\\n\\n要求： \\n- 故事应围绕从图片分析得出的主题和情感进行展开。\\n- 包含鲜明的人物、情节以及转折。\\n- 语言生动形象，能够引起读者的共鸣。\\n- 直接讲故事，不要提及图片。\"},{\"role\":\"user\",\"content\":\"{{readImg}}\"}]},\"inputParams\":[{\"field\":\"text\",\"name\":\"readImg\",\"nodeId\":\"165363942517174272\"}],\"outputParams\":[{\"field\":\"text\",\"name\":\"回复内容\",\"type\":\"string\"}],\"height\":136,\"width\":332}}],\"edges\":[{\"id\":\"165363942525562880\",\"type\":\"base-edge\",\"sourceNodeId\":\"start-node\",\"targetNodeId\":\"165363942517174272\",\"sourceAnchorId\":\"start-node_output\",\"targetAnchorId\":\"165363942517174272_input\",\"pointsList\":[{\"x\":466,\"y\":457},{\"x\":566,\"y\":457},{\"x\":409,\"y\":304},{\"x\":509,\"y\":304}]},{\"id\":\"168280528428167168\",\"type\":\"base-edge\",\"sourceNodeId\":\"165363942517174272\",\"targetNodeId\":\"168280528419778560\",\"sourceAnchorId\":\"165363942517174272_output\",\"targetAnchorId\":\"168280528419778560_input\",\"pointsList\":[{\"x\":841,\"y\":304},{\"x\":941,\"y\":304},{\"x\":797,\"y\":551},{\"x\":897,\"y\":551}]},{\"id\":\"168280631234752512\",\"type\":\"base-edge\",\"sourceNodeId\":\"168280528419778560\",\"targetNodeId\":\"165364368465522688\",\"sourceAnchorId\":\"168280528419778560_output\",\"targetAnchorId\":\"165364368465522688_input\",\"pointsList\":[{\"x\":1229,\"y\":551},{\"x\":1329,\"y\":551},{\"x\":1254,\"y\":426},{\"x\":1354,\"y\":426}]}]}', 'enable', '{\"outputs\":[{\"field\":\"outputText\",\"type\":\"string\"}],\"inputs\":[{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\"},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\"},{\"field\":\"images\",\"name\":\"图片\",\"type\":\"picture\"}]}');
INSERT INTO `airag_flow` VALUES ('1904779811574784002', 'jeecg', '2025-03-26 14:17:51', 'jeecg', '2025-03-27 16:44:53', 'A04', NULL, 'jeecg', '示例_OCR', '', 'https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/1dataOCR_1743065089791.png', 'THEN(\n    start.tag(\'start-node\'),\n    SWITCH(switch.tag(\'167880707187527680\')).to(\n        end.tag(\'167880856269869056\'),\n        THEN(\n            code_167881149430747136.tag(\'code_167881149430747136\'),\n            llm.tag(\'167881839356006400\'),\n            end.tag(\'167880661561888768\')\n        ).tag(\"code_167881149430747136\")\n    ).tag(\'167880707187527680\')\n).tag(\"start-node\")', '{\"nodes\":[{\"id\":\"start-node\",\"type\":\"start\",\"x\":300,\"y\":406,\"properties\":{\"text\":\"开始\",\"remarks\":\"\",\"options\":{},\"inputParams\":[{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\",\"required\":true},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\",\"required\":true},{\"field\":\"images\",\"name\":\"图片\",\"type\":\"picture\",\"required\":true}],\"outputParams\":[],\"height\":62,\"width\":332}},{\"id\":\"167880661561888768\",\"type\":\"end\",\"x\":1474,\"y\":316,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":false,\"outputContent\":\"\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"text\",\"name\":\"data\",\"nodeId\":\"167881839356006400\"}],\"height\":62,\"width\":332}},{\"id\":\"167880707187527680\",\"type\":\"switch\",\"x\":681,\"y\":233,\"properties\":{\"text\":\"条件分支\",\"options\":{\"if\":[{\"logic\":\"AND\",\"conditions\":[{\"nodeId\":\"start-node\",\"field\":\"images\",\"operator\":\"EMPTY\",\"value\":\"\"}],\"next\":\"167880856269869056\"}],\"else\":{\"next\":\"code_167881149430747136\"}},\"inputParams\":[],\"outputParams\":[{\"field\":\"index\",\"name\":\"分支索引\",\"type\":\"number\"}],\"height\":118,\"width\":332}},{\"id\":\"167880856269869056\",\"type\":\"end\",\"x\":1207,\"y\":181,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":true,\"outputContent\":\"{\\n    \\\"message\\\": \\\"请提供图片\\\"\\n  }\"},\"inputParams\":[],\"outputParams\":[],\"height\":62,\"width\":332}},{\"id\":\"code_167881149430747136\",\"type\":\"code\",\"x\":937,\"y\":412,\"properties\":{\"text\":\"脚本执行\",\"options\":{\"codeType\":\"javascript\",\"code\":\"function main(params) {\\n  let newQuestion = params.question\\n  if(!params.question){\\n    newQuestion = \\\"从图片中提取文字\\\"\\n  }\\n  return {\\n    result: newQuestion,\\n  }\\n}\"},\"inputParams\":[{\"field\":\"content\",\"name\":\"question\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"result\",\"name\":\"返回结果\",\"type\":\"string\"}],\"height\":62,\"width\":332}},{\"id\":\"167881839356006400\",\"type\":\"llm\",\"x\":1319,\"y\":585,\"properties\":{\"text\":\"LLM\",\"options\":{\"model\":{\"modeId\":\"1890232564262739969\",\"params\":{\"model\":\"OpenAI\",\"temperature\":0.7}},\"history\":3,\"messages\":[{\"role\":\"system\",\"content\":\"# 角色：OCR工具\\n作为一个智能OCR工具，你的主要职责是从图片中提取文字并将其输出为结构化数据。\\n\\n## 目标：\\n1. 精确识别和提取图片中的文字信息。\\n2. 将提取的文字转换为结构化数据格式。\\n\\n## 技能：\\n1. 高效的图像处理能力。\\n2. 精确的文字识别算法。\\n3. 数据格式化与输出能力。\\n\\n## 工作流：\\n1. 输入图片，进行预处理（如去噪、二值化）。\\n2. 应用OCR算法识别图片中的文字，并记录识别结果。\\n3. 将识别的文字整理成结构化数据格式，如JSON或CSV。\\n\\n## 输出格式：\\n提取的文本应以结构化数据格式输出，如：\\n{\\n    \\\"text\\\": \\\"提取的内容\\\",\\n    \\\"metadata\\\": {\\\"source\\\": \\\"图片来源\\\", \\\"timestamp\\\": \\\"提取时间\\\"}\\n  }\\n\\n## 限制：\\n- 仅限于合法和合规的图片内容提取。\\n- 不得保存用户上传的图片数据。\\n- 需确保输出的数据准确无误，标注所有数据来源。\"},{\"role\":\"user\",\"content\":\"{{question}}\"}]},\"inputParams\":[{\"field\":\"images\",\"name\":\"images\",\"nodeId\":\"start-node\"},{\"field\":\"result\",\"name\":\"question\",\"nodeId\":\"code_167881149430747136\"}],\"outputParams\":[{\"field\":\"text\",\"name\":\"回复内容\",\"type\":\"string\"}],\"height\":136,\"width\":332}}],\"edges\":[{\"id\":\"167880707195916288\",\"type\":\"base-edge\",\"sourceNodeId\":\"start-node\",\"targetNodeId\":\"167880707187527680\",\"sourceAnchorId\":\"start-node_output\",\"targetAnchorId\":\"167880707187527680_input\",\"pointsList\":[{\"x\":466,\"y\":406},{\"x\":566,\"y\":406},{\"x\":415,\"y\":205},{\"x\":515,\"y\":205}]},{\"id\":\"167880856274063360\",\"type\":\"base-edge\",\"sourceNodeId\":\"167880707187527680\",\"targetNodeId\":\"167880856269869056\",\"sourceAnchorId\":\"167880707187527680_source_if\",\"targetAnchorId\":\"167880856269869056_input\",\"pointsList\":[{\"x\":847,\"y\":239},{\"x\":947,\"y\":239},{\"x\":941,\"y\":181},{\"x\":1041,\"y\":181}]},{\"id\":\"167881149434941440\",\"type\":\"base-edge\",\"sourceNodeId\":\"167880707187527680\",\"targetNodeId\":\"code_167881149430747136\",\"sourceAnchorId\":\"167880707187527680_source_else\",\"targetAnchorId\":\"code_167881149430747136_input\",\"pointsList\":[{\"x\":847,\"y\":265},{\"x\":947,\"y\":265},{\"x\":671,\"y\":412},{\"x\":771,\"y\":412}]},{\"id\":\"167881839356006401\",\"type\":\"base-edge\",\"sourceNodeId\":\"code_167881149430747136\",\"targetNodeId\":\"167881839356006400\",\"sourceAnchorId\":\"code_167881149430747136_output\",\"targetAnchorId\":\"167881839356006400_input\",\"pointsList\":[{\"x\":1103,\"y\":412},{\"x\":1203,\"y\":412},{\"x\":1053,\"y\":548},{\"x\":1153,\"y\":548}]},{\"id\":\"167882293611712512\",\"type\":\"base-edge\",\"sourceNodeId\":\"167881839356006400\",\"targetNodeId\":\"167880661561888768\",\"sourceAnchorId\":\"167881839356006400_output\",\"targetAnchorId\":\"167880661561888768_input\",\"pointsList\":[{\"x\":1485,\"y\":548},{\"x\":1585,\"y\":548},{\"x\":1208,\"y\":316},{\"x\":1308,\"y\":316}]}]}', 'enable', '{\"outputs\":[{\"field\":\"text\",\"name\":\"data\",\"nodeId\":\"167881839356006400\"},{\"field\":\"outputText\",\"type\":\"string\"}],\"inputs\":[{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\"},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\"},{\"field\":\"images\",\"name\":\"图片\",\"type\":\"picture\"}]}');
INSERT INTO `airag_flow` VALUES ('1905158829855784962', 'jeecg', '2025-03-27 15:23:56', 'jeecg', '2025-03-27 16:29:22', 'A04', NULL, 'jeecg', '示例_翻译', '', 'https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/翻译_1743060940605.png', 'THEN(\n    start.tag(\'start-node\'),\n    SWITCH(switch.tag(\'168262809717821440\')).to(\n        end.tag(\'168259683329757184\'),\n        THEN(\n            SWITCH(classifier.tag(\'168263048935755776\')).to(\n                llm.tag(\'168263321821368320\'),\n                llm.tag(\'168263346282549248\')\n            ).tag(\'168263048935755776\'),\n            end.tag(\'168263794896916480\')\n        ).tag(\"168263048935755776\")\n    ).tag(\'168262809717821440\')\n).tag(\"start-node\")', '{\"nodes\":[{\"id\":\"start-node\",\"type\":\"start\",\"x\":300,\"y\":457,\"properties\":{\"text\":\"开始\",\"remarks\":\"\",\"options\":{},\"inputParams\":[{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\",\"required\":true},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\",\"required\":true}],\"outputParams\":[],\"height\":62,\"width\":332}},{\"id\":\"168259683329757184\",\"type\":\"end\",\"x\":1090,\"y\":150,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":false,\"outputContent\":\"\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"content\",\"name\":\"data\",\"nodeId\":\"start-node\"}],\"height\":62,\"width\":332}},{\"id\":\"168262809717821440\",\"type\":\"switch\",\"x\":701,\"y\":281,\"properties\":{\"text\":\"条件分支\",\"options\":{\"if\":[{\"logic\":\"AND\",\"conditions\":[{\"nodeId\":\"start-node\",\"field\":\"content\",\"operator\":\"EMPTY\",\"value\":\"\"}],\"next\":\"168259683329757184\"}],\"else\":{\"next\":\"168263048935755776\"}},\"inputParams\":[],\"outputParams\":[{\"field\":\"index\",\"name\":\"分支索引\",\"type\":\"number\"}],\"height\":118,\"width\":332}},{\"id\":\"168263048935755776\",\"type\":\"classifier\",\"x\":1086,\"y\":381,\"properties\":{\"text\":\"分类器\",\"options\":{\"model\":{\"modeId\":\"1890232564262739969\",\"params\":{\"model\":\"OpenAI\",\"temperature\":0.2}},\"categories\":[{\"category\":\"是中文\",\"next\":\"168263321821368320\"}],\"else\":{\"next\":\"168263346282549248\"}},\"inputParams\":[{\"field\":\"content\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"index\",\"name\":\"分类索引\",\"type\":\"number\"},{\"field\":\"content\",\"name\":\"分类内容\",\"type\":\"string\"}],\"height\":118,\"width\":332}},{\"id\":\"168263321821368320\",\"type\":\"llm\",\"x\":1513,\"y\":292,\"properties\":{\"text\":\"翻译成英文\",\"options\":{\"model\":{\"modeId\":\"1890232564262739969\",\"params\":{\"model\":\"OpenAI\",\"temperature\":0.3}},\"history\":1,\"messages\":[{\"role\":\"system\",\"content\":\"将用户输入完整翻译成英文，包括所有语气词和重复表达\\n- 严格保留原始语序和强调成分\\n- 禁止省略任何字词或改变语气强度\\n- 直接输出翻译结果不做解释\"},{\"role\":\"user\",\"content\":\"{{content}}\"}]},\"inputParams\":[{\"field\":\"content\",\"name\":\"content\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"text\",\"name\":\"回复内容\",\"type\":\"string\"}],\"height\":136,\"width\":332}},{\"id\":\"168263346282549248\",\"type\":\"llm\",\"x\":1514,\"y\":489,\"properties\":{\"text\":\"翻译成中文\",\"options\":{\"model\":{\"modeId\":\"1890232564262739969\",\"params\":{\"model\":\"OpenAI\",\"temperature\":0.3}},\"history\":1,\"messages\":[{\"role\":\"system\",\"content\":\"将用户输入完整翻译成中文，包括所有语气词和重复表达\\n- 严格保留原始语序和强调成分\\n- 禁止省略任何字词或改变语气强度\\n- 直接输出翻译结果不做解释\"},{\"role\":\"user\",\"content\":\"{{content}}\"}]},\"inputParams\":[{\"field\":\"content\",\"name\":\"content\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"text\",\"name\":\"回复内容\",\"type\":\"string\"}],\"height\":136,\"width\":332}},{\"id\":\"168263794896916480\",\"type\":\"end\",\"x\":1982,\"y\":360,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":true,\"outputContent\":\"{{dataC}}{{dataE}}\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"text\",\"name\":\"dataC\",\"nodeId\":\"168263346282549248\"},{\"field\":\"text\",\"name\":\"dataE\",\"nodeId\":\"168263321821368320\"}],\"height\":62,\"width\":332}}],\"edges\":[{\"id\":\"168262809722015744\",\"type\":\"base-edge\",\"sourceNodeId\":\"start-node\",\"targetNodeId\":\"168262809717821440\",\"sourceAnchorId\":\"start-node_output\",\"targetAnchorId\":\"168262809717821440_input\",\"pointsList\":[{\"x\":466,\"y\":457},{\"x\":566,\"y\":457},{\"x\":435,\"y\":253},{\"x\":535,\"y\":253}]},{\"id\":\"168262871336341504\",\"type\":\"base-edge\",\"sourceNodeId\":\"168262809717821440\",\"targetNodeId\":\"168259683329757184\",\"sourceAnchorId\":\"168262809717821440_source_if\",\"targetAnchorId\":\"168259683329757184_input\",\"pointsList\":[{\"x\":867,\"y\":287},{\"x\":967,\"y\":287},{\"x\":824,\"y\":150},{\"x\":924,\"y\":150}]},{\"id\":\"168263048939950080\",\"type\":\"base-edge\",\"sourceNodeId\":\"168262809717821440\",\"targetNodeId\":\"168263048935755776\",\"sourceAnchorId\":\"168262809717821440_source_else\",\"targetAnchorId\":\"168263048935755776_input\",\"pointsList\":[{\"x\":867,\"y\":313},{\"x\":967,\"y\":313},{\"x\":820,\"y\":353},{\"x\":920,\"y\":353}]},{\"id\":\"168263321825562624\",\"type\":\"base-edge\",\"sourceNodeId\":\"168263048935755776\",\"targetNodeId\":\"168263321821368320\",\"sourceAnchorId\":\"168263048935755776_case_1\",\"targetAnchorId\":\"168263321821368320_input\",\"pointsList\":[{\"x\":1252,\"y\":387},{\"x\":1352,\"y\":387},{\"x\":1247,\"y\":255},{\"x\":1347,\"y\":255}]},{\"id\":\"168263346286743552\",\"type\":\"base-edge\",\"sourceNodeId\":\"168263048935755776\",\"targetNodeId\":\"168263346282549248\",\"sourceAnchorId\":\"168263048935755776_case_else\",\"targetAnchorId\":\"168263346282549248_input\",\"pointsList\":[{\"x\":1252,\"y\":413},{\"x\":1352,\"y\":413},{\"x\":1248,\"y\":452},{\"x\":1348,\"y\":452}]},{\"id\":\"168263794901110784\",\"type\":\"base-edge\",\"sourceNodeId\":\"168263346282549248\",\"targetNodeId\":\"168263794896916480\",\"sourceAnchorId\":\"168263346282549248_output\",\"targetAnchorId\":\"168263794896916480_input\",\"pointsList\":[{\"x\":1680,\"y\":452},{\"x\":1780,\"y\":452},{\"x\":1716,\"y\":360},{\"x\":1816,\"y\":360}]},{\"id\":\"168263831215394816\",\"type\":\"base-edge\",\"sourceNodeId\":\"168263321821368320\",\"targetNodeId\":\"168263794896916480\",\"sourceAnchorId\":\"168263321821368320_output\",\"targetAnchorId\":\"168263794896916480_input\",\"pointsList\":[{\"x\":1679,\"y\":255},{\"x\":1779,\"y\":255},{\"x\":1716,\"y\":360},{\"x\":1816,\"y\":360}]}]}', 'enable', '{\"outputs\":[{\"field\":\"outputText\",\"type\":\"string\"},{\"field\":\"content\",\"name\":\"data\",\"nodeId\":\"start-node\"}],\"inputs\":[{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\"},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\"}]}');
INSERT INTO `airag_flow` VALUES ('1905189468558671874', 'jeecg', '2025-03-27 17:25:41', 'jeecg', '2025-03-27 17:40:51', 'A04', NULL, 'jeecg', '示例_PMP考试宝典', '', 'https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/pmp_1743067580648.png', 'THEN(\n    start.tag(\'start-node\'),\n    WHEN(\n        knowledge.tag(\'168290518600351744\'),\n        llm.tag(\'168290871702028288\')\n    ).tag(\"168290518600351744\"),\n    llm.tag(\'168290861241434112\'),\n    end.tag(\'168290315671535616\')\n).tag(\"start-node\")', '{\"nodes\":[{\"id\":\"start-node\",\"type\":\"start\",\"x\":300,\"y\":397,\"properties\":{\"text\":\"开始\",\"remarks\":\"\",\"options\":{},\"inputParams\":[{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\",\"required\":true},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\",\"required\":true}],\"outputParams\":[],\"height\":92,\"width\":332}},{\"id\":\"168290315671535616\",\"type\":\"end\",\"x\":1644,\"y\":348,\"properties\":{\"text\":\"结束\",\"options\":{\"outputText\":true,\"outputContent\":\"{{res}}\"},\"inputParams\":[],\"outputParams\":[{\"field\":\"text\",\"name\":\"res\",\"nodeId\":\"168290861241434112\"}],\"height\":92,\"width\":332}},{\"id\":\"168290518600351744\",\"type\":\"knowledge\",\"x\":693,\"y\":209,\"properties\":{\"text\":\"知识库\",\"options\":{\"knowIds\":[\"1905186756806918146\"],\"topNumber\":5,\"similarity\":0.7},\"inputParams\":[{\"field\":\"content\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"documents\",\"name\":\"文档列表\",\"type\":\"object[]\"},{\"field\":\"data\",\"name\":\"文档内容\",\"type\":\"string\"}],\"height\":92,\"width\":332}},{\"id\":\"168290861241434112\",\"type\":\"llm\",\"x\":1181,\"y\":350,\"properties\":{\"text\":\"总结LLM\",\"options\":{\"model\":{\"modeId\":\"1890232564262739969\",\"params\":{\"model\":\"OpenAI\",\"temperature\":0.4}},\"history\":3,\"messages\":[{\"role\":\"system\",\"content\":\"你是一个智能知识助手，旨在综合知识库和大型语言模型(LLM)的返回数据，以高效、准确地回答用户提出的问题。请遵循以下要求：\\n\\n## 目标：\\n- 提供准确、相关且易于理解的回答，结合知识库和LLM的信息。\\n\\n## 技能：\\n1. 能够快速检索并整合来自不同知识库的信息。\\n2. 理解用户问题的上下文，并提供清晰的答案。\\n3. 具备自然语言处理能力，以便流畅表达复杂信息。\\n\\n## 工作流：\\n1. 接收用户问题并进行解析，识别关键要素。\\n2. 从综合知识库和LLM中获取相关数据，确保信息的准确性和完整性。\\n3. 将获取的信息进行整合，形成清晰、简洁的回答。\\n\\n## 输出格式：\\n- 每次回答应以简洁明了的句子呈现，必要时可以添加示例或补充信息。\\n\\n## 限制：\\n- 不得提供未经验证的信息或个人隐私数据。\\n- 所有数据需标注来源，不确定信息用[需核实]标记。\\n- 自动过滤涉及偏见或违法内容，替换为[合规表达]。\"},{\"role\":\"user\",\"content\":\"知识库返回数据：{{knowRes}}\\n\\nLLM返回数据：{{llmRes}}\\n用户问题：{{userQue}}\"}]},\"inputParams\":[{\"field\":\"data\",\"name\":\"knowRes\",\"nodeId\":\"168290518600351744\"},{\"field\":\"text\",\"name\":\"llmRes\",\"nodeId\":\"168290871702028288\"},{\"field\":\"content\",\"name\":\"userQue\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"text\",\"name\":\"回复内容\",\"type\":\"string\"}],\"height\":166,\"width\":332}},{\"id\":\"168290871702028288\",\"type\":\"llm\",\"x\":692,\"y\":521,\"properties\":{\"text\":\"LLM\",\"options\":{\"model\":{\"modeId\":\"1890232564262739969\",\"params\":{\"model\":\"OpenAI\",\"temperature\":0.7}},\"history\":3,\"messages\":[{\"role\":\"system\",\"content\":\"# 角色：PMP知识专家\\nPMP知识专家致力于项目管理知识的传播与应用，帮助项目经理提升技能和管理能力。\\n\\n## 目标：\\n1. 为项目管理提供权威的知识支持。\\n2. 帮助项目经理解决在项目管理中遇到的实际问题。\\n\\n## 技能：\\n1. 精通项目管理的各项理论和工具。\\n2. 熟悉PMP认证流程及考试内容。\\n3. 能够进行项目风险评估与管理。\\n\\n## 工作流：\\n1. 评估项目经理的需求与挑战，识别关键问题。\\n2. 提供相关的项目管理知识、工具和最佳实践建议。\\n3. 指导项目经理制定和实施有效的项目管理计划。\\n\\n## 输出格式：\\n- 提供清晰的建议与解决方案，使用简洁明了的语言，适合项目经理理解和应用。\\n\\n## 限制：\\n- 所有建议需基于现有的PMP知识体系，避免个人主观意见。\\n- 不得提供未经验证的信息或数据，所有数据需标注来源，需核实的信息用[需核实]标记。\"},{\"role\":\"user\",\"content\":\"{{question}}\"}]},\"inputParams\":[{\"field\":\"content\",\"name\":\"question\",\"nodeId\":\"start-node\"}],\"outputParams\":[{\"field\":\"text\",\"name\":\"回复内容\",\"type\":\"string\"}],\"height\":166,\"width\":332}}],\"edges\":[{\"id\":\"168290518604546048\",\"type\":\"base-edge\",\"sourceNodeId\":\"start-node\",\"targetNodeId\":\"168290518600351744\",\"sourceAnchorId\":\"start-node_output\",\"targetAnchorId\":\"168290518600351744_input\",\"pointsList\":[{\"x\":466,\"y\":382},{\"x\":566,\"y\":382},{\"x\":427,\"y\":194},{\"x\":527,\"y\":194}]},{\"id\":\"168290861245628416\",\"type\":\"base-edge\",\"sourceNodeId\":\"168290518600351744\",\"targetNodeId\":\"168290861241434112\",\"sourceAnchorId\":\"168290518600351744_output\",\"targetAnchorId\":\"168290861241434112_input\",\"pointsList\":[{\"x\":859,\"y\":194},{\"x\":959,\"y\":194},{\"x\":915,\"y\":298},{\"x\":1015,\"y\":298}]},{\"id\":\"168290871706222592\",\"type\":\"base-edge\",\"sourceNodeId\":\"start-node\",\"targetNodeId\":\"168290871702028288\",\"sourceAnchorId\":\"start-node_output\",\"targetAnchorId\":\"168290871702028288_input\",\"pointsList\":[{\"x\":466,\"y\":382},{\"x\":566,\"y\":382},{\"x\":426,\"y\":469},{\"x\":526,\"y\":469}]},{\"id\":\"168291272883011584\",\"type\":\"base-edge\",\"sourceNodeId\":\"168290871702028288\",\"targetNodeId\":\"168290861241434112\",\"sourceAnchorId\":\"168290871702028288_output\",\"targetAnchorId\":\"168290861241434112_input\",\"pointsList\":[{\"x\":858,\"y\":469},{\"x\":958,\"y\":469},{\"x\":915,\"y\":298},{\"x\":1015,\"y\":298}]},{\"id\":\"168292930635530240\",\"type\":\"base-edge\",\"sourceNodeId\":\"168290861241434112\",\"targetNodeId\":\"168290315671535616\",\"sourceAnchorId\":\"168290861241434112_output\",\"targetAnchorId\":\"168290315671535616_input\",\"pointsList\":[{\"x\":1347,\"y\":298},{\"x\":1447,\"y\":298},{\"x\":1378,\"y\":333},{\"x\":1478,\"y\":333}]}]}', 'enable', '{\"outputs\":[{\"field\":\"outputText\",\"type\":\"string\"}],\"inputs\":[{\"field\":\"content\",\"name\":\"用户问题\",\"type\":\"string\"},{\"field\":\"history\",\"name\":\"历史记录\",\"type\":\"string[]\"}]}');

-- ----------------------------
-- Table structure for airag_knowledge
-- ----------------------------
CREATE TABLE `airag_knowledge`  (
                                    `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                    `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
                                    `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
                                    `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
                                    `update_time` datetime NULL DEFAULT NULL COMMENT '更新日期',
                                    `sys_org_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所属部门',
                                    `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '租户id',
                                    `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '知识库名称',
                                    `descr` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '描述',
                                    `embed_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '向量模型id',
                                    `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '状态',
                                    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of airag_knowledge
-- ----------------------------
INSERT INTO `airag_knowledge` VALUES ('1897212906878009346', 'jeecg', '2025-03-05 17:09:40', NULL, NULL, 'A04', NULL, '积木报表文档', '积木报表文档', '1891459707122499586', 'enable');
INSERT INTO `airag_knowledge` VALUES ('1897926563148648449', 'jeecg', '2025-03-07 16:25:29', 'jeecg', '2025-03-11 10:04:25', 'A04', NULL, 'JeecgBoot文档', 'JeecgBoot文档', '1891459707122499586', 'enable');
INSERT INTO `airag_knowledge` VALUES ('1905186756806918146', 'jeecg', '2025-03-27 17:14:54', NULL, NULL, 'A04', NULL, 'PMP', NULL, '1891459707122499586', 'enable');

-- ----------------------------
-- Table structure for airag_knowledge_doc
-- ----------------------------
CREATE TABLE `airag_knowledge_doc`  (
                                        `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                        `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
                                        `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
                                        `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
                                        `update_time` datetime NULL DEFAULT NULL COMMENT '更新日期',
                                        `sys_org_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所属部门',
                                        `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '租户id',
                                        `knowledge_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '知识库id',
                                        `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标题',
                                        `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '类型',
                                        `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '内容',
                                        `status` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '状态',
                                        `metadata` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '元数据',
                                        PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of airag_knowledge_doc
-- ----------------------------
INSERT INTO `airag_knowledge_doc` VALUES ('1897213100944261121', 'jeecg', '2025-03-05 17:10:26', 'admin', '2025-04-02 23:53:30', 'A04', NULL, '1897212906878009346', 'qa', 'text', '常见问题\n遇到问题请先升级至最新版，仍未解决可向团队反馈 点击反馈问题\n\n1. 积木报表是免费吗？\n回答： 积木报表代码不开源，但是功能可以免费使用。\n\n大屏支持离线安装，积木BI的推出，可以永久免费使用。\n针对公司用户我们提供企业版，免费版本也会持续发布。\n2. 功能操作提示 没有权限，请联系管理员分配权限！\n回答：这是因为报表针对敏感接口加了角色和权限控制，需要进行内置角色权限集成，具体见文档权限集成配置(重要)\n\n3. 积木报表怎么独立运行？\nDocker方式启动\n集成Demo启动\n4. 启动报mongo错误\n启动报错：\norg.mongodb.driver.cluster : Exception in monitor thread while connecting to \nserver localhost:27017 while accessing MongoDB with Java\n\n解决方案： 排除mongo启动默认加载 MongoAutoConfiguration\n@SpringBootApplication\n@EnableAutoConfiguration(exclude={MongoAutoConfiguration.class})\n\n5. 报表配置JeecgBoot菜单\n{{ window._CONFIG[\'domianURL\'] }}/jmreport/list?token=${token}\n\n参数说明\n\n{{ window._CONFIG[\'domianURL\'] }} ：后台项目访问地址\n${token} ：登录token用于权限控制\n前端组件：layouts/IframePageView 菜单配置截图\n\n\n6. 积木报表数据源支持哪些数据库？\n数据库	支持\nMySQL	√\nOracle、Oracle9i	√\nSqlServer、SqlServer2012	√\nPostgreSQL	√\nDB2、Informix	√\nMariaDB	√\nSQLite、Hsqldb、Derby、H2	√\n达梦、人大金仓、神通	√\n华为高斯、虚谷、瀚高数据库、 TDengine 涛思数据	√\n阿里云PolarDB、PPAS、HerdDB	√\nHive、HBase、CouchBase	√\n导入Excel、csv、json文件数据集	√\nsqllite、TiDB、Doris、clickhouse、 MongoDB-BI	√\nelasticsearch、mogodb	√\n积木平台暂时只提供mysql脚本，其他数据库请自转 Navicat工具mysql转库oracle步骤\n7. API数据源怎样实现条件查询？\n具体请看查询条件设置\n\n8.报表集成到自己的springboot项目\n请求参数如果后台接收的实体属性里没有，后台报错：not marked as ignorable\n\n\n\n解决方法：增加application.yml的配置jackson.fail_on_unknown_properties: false\n\n\n\n9.积木报表SQL数据集中sql语句加上limit在数据预览中报错?\nsql语句写法:\n\n 报错截图：\n\n\n\n原因是sql语句末尾加上了limit，我们在后台已经默认分页，故不用在末尾加上limit，否则会报错\n\n10.如何去掉打印页面的页眉、页脚？\n在打印弹窗页面，点击“更多设置 ->选项”，去掉“页眉和页脚”前边的对勾，打印界面就不显示页眉和页脚了；\n\n\n\n11.报表能否集成到vue项目中?\n不能集成到前端项目，因为积木报表提供的JAVA依赖，只能集成到JAVA项目中。\n\n12.sql或者api解析失败的问题\nsql或者api必须有查询结果才行，不然无法解析字段 相关issue #2305\n\n13.怎样自定义打印页面设置？\n打印区域除了可以手动选择“A4、A3...”，还可以根据自己需求，自定义大小。\n\n操作参考打印区域设置\n\n14.预览时，列表数组在预览界面怎么只显示一条数据？\n（1）检查在数据集解析的时候 ，是否勾选“是否列表”；\n\n\n\n（2）设计界面拖过来的数据字段，是否为#开头；\n\n\n\n15.横向动态列分组怎么设计？\n操作参考文档\n\n16.预览页面多内容，但设计界面没有，怎么处理？\n错误样式图：\n\n\n\n解决方案： 选中多出来的地方（可多选一些地方），右键点击：删除数据，就没有了；\n\n\n\n17.积木报表SQL数据集中数据预览为什么只显示10条数据?\n为了避免大数据问题，故只取前10条数据进行展示\n\n\n\n18. 积木报表数据源怎么配置？\n添加数据源文档\n\n19. 怎样把报表集成到JeecgBoot的菜单中？\n备注：大屏和报表的操作是一样的；\n\n（1）复制报表访问链接 （2）在系统管理菜单管理进行配置 （3）点击新增按钮填写信息\n\n注意：\na) 前端组件必须按照格式填写 layouts/IframePageView *用window._CONFIG[\'domianURL\']代替IP地址、端口号和项目名称,并用{{}}包起来;\nb）末尾必须携带参数，如（?sex）；\nc) 是否为路由菜单：是；\n\n\n\n\n（4）角色授权 路径：在系统管理->角色授权找到自己对应的角色,鼠标放到更多->授权；\n\n勾选刚才创建的菜单\n刷新页面即可看见点击菜单\n\n\n20. 数据集配置点击确认会报错\nhttps://github.com/jeecgboot/JimuReport/issues/439\nSQL state \\[null\\]; error code \\[0\\]; Error; nested exception is java.sql.SQLException: Error\n\n那么就查看mysql数据库连接驱动是版本是5.1.47,如果是那么请将驱动升级版本或降低版本，如：\n\n<dependency>\n    <groupId>mysql</groupId>\n    <artifactId>mysql-connector-java</artifactId>\n    <version>5.1.46</version>\n    <optional>true</optional>\n    <scope>runtime</scope>\n</dependency>\n\n22.如何把SQL数据集拼接的查询条件加到数据源语法的group by前面\n参考报表参数设置\n\n23.预览页面与设计页面不一致，在预览时出现空白行\n检查数据集是多条数据的集合，还是单条数据的对象；如果是集合使用#，如果是对象则需要使用$ 如果页面多行使用#，则会被当做多个集合，中间自动填充空白行。\n\n\n\n24.为什么配置参数后勾选查询后，下拉单选变成输入框\n参数不是字段，无法进行配置后就可以下拉单选；可配置字典code实现下拉\n\n25.一页展示一条数据，进行循环打印\n可将整页作为循环块，设置为循环块 参考文档：点击查看\n\n26.mysql数据库类型tyint被转换成了true和false\n需要在维护界面，数据源地址出拼接上\n\ntinyInt1isBit=false\n\n\n\n27.数据库里图片字段为图片链接，如何展示在报表中\n添加数据源取出图片字段，将单元格类型设置为图片即可，如下图：\n\n\n\n28. 达梦数据库提示表名不存在\n 因为达梦数据库如果不是当前用户名登录的(如SYSDBA)，访问不同名的（除了SYSDBA）外，均需要模式名.表名，那么需要你如下图操作，在同名下新建表\n\n\n\n29. 积木官网添加数据源\n积木官网添加数据源需使用远程地址，不可使用localhost。\n\n32.字典code中直接输入sql语句，下拉框单选项乱序\n解决方案：可以填写 order by 进行自定义排序，如\n\nselect dict_code as value,dict_name as name from jimu_dict order by create_time\n\n注意：如果在sqlserver下需要加上top 10（10代表多少条），不然会报错，如\n\nselect top 10 dict_code as value,dict_name as name from jimu_dict order by create_time\n\n33.导出excel报错版本不匹配，java.lang.NoSuchMethodError\n将poi版本升级到4.1.2即可解决\n\n34. 如何增加列数\n列索引数量可根据需要修改 参考文档：点击查看\n\n\n\n35.sql数据集下拉选择数据源，下方列表显示空白，但是有数据\n目前为了统一规则后台返回的数据的对象均为小写(name)，如果规则不匹配，请改成小写\n\n\n\n38.预览界面查询栏如何设置默认展开？\n解决方案：设置JS增强\n\n\n\nfunction init(){\n  this.queryPanel = \'1\';\n}\n\n39.sqlServer存储过程中有临时表获取不到数据\n可以通过set nocount on来解决\n\n 相关issue: https://github.com/jeecgboot/JimuReport/issues/726\n\n40.若依集成积木报表1.4.4+ 新建报表报错\nfreemarker.core.InvalidReferenceException\n\n升级fastjson到1.2.78\n\n<dependency>\n   <groupId>com.alibaba</groupId>\n   <artifactId>fastjson</artifactId>\n   <version>1.2.78</version>\n</dependency>\n\n相关issue：issue\n\n41.模板示例中条件查询预览失败\n没有对应的表\n\n42.打印的时候，字体加粗效果丢失\n宋体打印不支持加粗，换成默认的字体\n\n43.sqlserver提示驱动不存在\n在pom文件中添加sqlserver依赖\n\n   <dependency>\n        <groupId>com.microsoft.sqlserver</groupId>\n        <artifactId>sqljdbc4</artifactId>\n        <version>4.0</version>\n        <optional>true</optional>\n        <scope>runtime</scope>\n    </dependency>     \n\n44.sqlserver下使用CONVERT函数注意事项\n不可与order by一起使用\nCONVERT函数需指定别名 如：CONVERT(varchar(7),CREATE_TIME) as CREATE_TIME\n45.能否设置隐藏的查询条件\n问题描述： 同一报表，希望不同的人看到不同的数据，目前可以通过JS增强设置初始值，但又不想让用户修改，能否提供设置查询条件隐藏的功能，这样便于数据权限的控制。 分析说明：此问题目的在于不同的人看不同的数据，提问人想设置查询条件默认值且不允许修改\n\n1.不同的人看不同的数据：可以使用系统变量 参考文档 如：\n\nsql数据集：select * from demo where create_by = \'#{sysUserCode}\'\napi数据集： http://xxx.xxx.xxx/query?create_by=#{sysUserCode}\n\n注意：此处的`sysUserCode`,是系统默认设置的登录人的账号，如果重写getUserInfo方法则需要重新设置，文档中的代码，只适用于jeecg-boot不可照搬，仅供参考【推荐此方案】。\n\n\n2.想设置查询条件默认值且不允许修改: js增强可以设置查询条件的默认值，也可以往查询参数对象里设置一个自定义的参数值，这个是支持的。但是，在配置数据集的时候，下方tab报表字段明细和报表参数中，会配置一些字段的信息，如果js增强定义的参数名不在这两个tab下，那么无效！所以做法如下：\n定义数据集（不需要将参数name设置为查询条件）：\nsql数据集：select * from demo where create_by = \'${name}\'\napi数据集： http://xxx.xxx.xxx/query?create_by=${name}\n\n定义js增强,设置name的值：\nfunction init(){\n    this.queryInfo[\'name\'] = \'scott\'\n}\n\n46. 日期默认查询，无法设置默认值为上月\n问题描述： 使用dateStr 默认取上月实现不了，用=concat(dateStr(\'yyyy\'),\'-\', dateStr(\'MM\', -1))返回2021-9，不是2021-09，少了一位。 建议实现=dateStr(\'yyyy-MM\'，-1) 返回 2021-09，而不是使用天数计算偏移量。\n解决方案： 参考文档 中的升级功能\n\n47. 打印多出一页空白纸张\n解决方案： 打印导出，空白行和没有行是有区别的，界面上都是空白没区别，但是实际数据存储，空白行会占位的。\n查看控制台打印的数据：你的rows都多达90多行了，说明是之前你设计的很多历史数据没有删除行，导致多出很多空白页。\n\n\n\n48. mongodb用法\n1). 以授权的方式启动Mongo,给使用的数据库添加用户\n\n切换数据库 use test\n\n创建用户 db.createUser({user: \"root\", pwd: \"123456\", roles: \\[{ role: \"dbOwner\", db: \"test\" }\\]})\n\n参考博客：https://www.cnblogs.com/jacksoft/p/6916137.html\n\n2). mongodb-driver-sync 驱动集成用法 参考博客： https://blog.csdn.net/nyzzht123/article/details/107936552 https://www.jianshu.com/p/5186fb5a1292\n\n49、出现jsqlparser不兼容问题\n如果出现jsqlparser不兼容问题，请这么引用\n<dependency>\n  <groupId>org.jeecgframework.jimureport</groupId>\n  <artifactId>jimureport-spring-boot-starter</artifactId>\n  <version>{版本号}</version>\n  <exclusions>\n    <exclusion>\n      <artifactId>minidao-spring-boot-starter</artifactId>\n      <groupId>org.jeecgframework</groupId>\n    </exclusion>\n  </exclusions>\n</dependency>\n<dependency>\n  <groupId>org.jeecgframework</groupId>\n  <artifactId>minidao-spring-boot-starter</artifactId>\n  <version>1.8.8</version>\n</dependency>\n\n50、关于积木报表在开发、生产环境增量同步https://github.com/jeecgboot/JimuReport/issues/1928\n51、数据库字段为关键词，字段作为查询条件报错\n报错信息：发现mysql下关键词字段\"year_month\"缺少\"`\"\n\nSELECT COUNT(1) total FROM ( select * from (select `year_month`,name,age from `demo`) jeecg_rp_temp  where year_month=? ) temp_count\n\n\n解决方案：关键词字段请用as重命名一下\n\n\n\n52、依赖redisson后编辑字典、查询字典报错：\n报错信息：\n\njava.lang.IllegalArgumentException: Cannot find cache named \'jmreport:cache:dict\' for Builder\n\n解决方法：配置文件增加：\n\nspring:\n  cache:\n    type: redis\n\n53、未登录的情况下导出excel和pdf报错\n解决方案：在SpringSecurityConfig页面排除导出excel和导出pdf的请求地址，其他同理\n\n\n\n .antMatchers(\"/jmreport/exportPdfStream\", \"/jmreport/exportAllExcelStream\")', 'building', NULL);
INSERT INTO `airag_knowledge_doc` VALUES ('1897926864815575042', 'jeecg', '2025-03-07 16:26:41', 'jeecg', '2025-03-10 17:28:33', 'A04', NULL, '1897926563148648449', 'index', 'file', '\n# 项目介绍\n\n\n `JeecgBoot` 是一款基于代码生成器的`低代码开发平台` 拥有零代码能力！采用前后端分离架构：SpringBoot2.x，Ant Design&Vue，Mybatis-plus，Shiro，JWT。强大的代码生成器让前后端代码一键生成，无需写任何代码! JeecgBoot引领新的开发模式(Online Coding模式-> 代码生成器模式-> 手工MERGE智能开发)， 帮助解决Java项目70%的重复工作，让开发更多关注业务逻辑。既能快速提高开发效率，帮助公司节省成本，同时又不失灵活性！JeecgBoot还独创在线开发模式（No-Code概念）：在线表单配置（表单设计器）、移动配置能力、工作流配置（在线设计流程）、报表配置能力、在线图表配置、插件能力（可插拔）等等！\n\n `JeecgBoot在提高UI能力`的同时，降低了前后分离的开发成本，JeecgBoot还独创在线开发模式（No-Code概念），一系列在线智能开发：在线配置表单、在线配置报表、在线图表设计、在线设计流程等等。\n\n ` JEECG宗旨是: `简单功能由Online Coding配置实现（在线配置表单、在线配置报表、在线图表设计、在线设计流程、在线设计表单），复杂功能由代码生成器生成进行手工Merge，既保证了智能又兼顾了灵活; \n\n 业务流程采用工作流来实现、扩展出任务接口，供开发编写业务逻辑，表单提供多种解决方案： 表单设计器、online配置表单、编码表单。同时实现了流程与表单的分离设计（松耦合）、并支持任务节点灵活配置，既保证了公司流程的保密性，又减少了开发人员的工作量。\n\n\n## 技术支持\n\n*  新手指南： [快速入门](http://www.jeecg.com/doc/quickstart)   |   [常见问题 ](http://www.jeecg.com/doc/qa)  | [版本日志](http://jeecg.com/doc/log)\n*  视频教程：[ JeecgBoot v3.7 新版视频教程](http://jeecg.com/doc/video)\n*  QQ交流群：⑩716488839、⑨808791225(满)、其他(满)\n*  在线演示 ：  [系统演示](http://boot3.jeecg.com)   |  [APP演示](http://app.jeecg.com)\n\n\n源码下载\n-----------------------------------\n\n- https://github.com/jeecgboot/jeecg-boot\n\n\n\n## 技术架构\n-----------------------------------\n\n#### 后端\n\n- IDE建议： IDEA (必须安装lombok插件 )\n- 语言：Java 8+ (支持17)\n- 依赖管理：Maven\n- 基础框架：Spring Boot 2.7.18\n- 微服务框架： Spring Cloud Alibaba 2021.0.1.0\n- 持久层框架：MybatisPlus 3.5.3.2\n- 报表工具： JimuReport 1.7.6\n- 安全框架：Apache Shiro 1.12.0，Jwt 3.11.0\n- 微服务技术栈：Spring Cloud Alibaba、Nacos、Gateway、Sentinel、Skywalking\n- 数据库连接池：阿里巴巴Druid 1.1.22\n- 日志打印：logback\n- 缓存：Redis\n- 其他：autopoi, fastjson，poi，Swagger-ui，quartz, lombok（简化代码）等。\n- 默认数据库脚本：MySQL5.7+\n- [其他数据库，需要自己转](https://my.oschina.net/jeecg/blog/4905722)\n\n\n#### 前端\n\n- 前端IDE建议：WebStorm、Vscode\n- 采用 Vue3.0+TypeScript+Vite+Ant-Design-Vue等新技术方案，包括二次封装组件、utils、hooks、动态菜单、权限校验、按钮级别权限控制等功能\n- 最新技术栈：Vue3.0 + TypeScript + Vite5 + ant-design-vue4 + pinia + echarts + unocss + vxe-table + qiankun + es6\n- 依赖管理：node、npm、pnpm\n\n\n\n#### 支持库\n\n|  数据库   |  支持   |\n| --- | --- |\n|   MySQL   |  √   |\n|  Oracle11g   |  √   |\n|  Sqlserver2017   |  √   |\n|   PostgreSQL   |  √   |\n|   MariaDB   |  √   |\n|   达梦   |  √   |\n|   人大金仓   |  √   |\n\n\n\n## 微服务解决方案\n\n\n- 1、服务注册和发现 Nacos √\n- 2、统一配置中心 Nacos  √\n- 3、路由网关 gateway(三种加载方式) √\n- 4、分布式 http feign √\n- 5、熔断降级限流 Sentinel √\n- 6、分布式文件 Minio、阿里OSS √ \n- 7、统一权限控制 JWT + Shiro √\n- 8、服务监控 SpringBootAdmin√\n- 9、链路跟踪 Skywalking   [参考文档](/java/springcloud/super/skywarking)\n- 10、消息中间件 RabbitMQ  √\n- 11、分布式任务 xxl-job  √ \n- 12、分布式事务 Seata\n- 13、轻量分布式日志 Loki+grafana套件\n- 14、支持 docker-compose、k8s、jenkins\n- 15、CAS 单点登录   √\n- 16、路由限流   √\n\n   \n### 微服务架构图\n![微服务架构图](https://jeecgos.oss-cn-beijing.aliyuncs.com/files/jeecgboot_springcloud2022.png \"在这里输入图片标题\")\n\n\n\n\n\n## 系统架构图\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/images/screenshot_1662547398792.png)\n*****\n\n\n## 系统截图\n\n### PC端\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687778397612.png)\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687778435846.png)\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687778476447.png)\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687778512836.png)\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687778582144.png)\n\n### 在线接口文档\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687778702243.png)\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687778737438.png)\n\n\n### 报表\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687778780458.png)\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/up-fa52b44445db281c51d3f267dce7450d21b.gif)\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687779705768.png)\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687779725144.png)\n\n### 流程\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687779807541.png)\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687779857971.png)\n\n![](/static/jimuImages/image_1687779966442.png)\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687780016598.png)\n\n\n### 手机端\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687780240854.png)\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687780264274.png)\n\n### PAD端\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687780285230.png)\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687780328101.png)\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687780342778.png)\n\n![](https://upload.jeecg.com/jeecg/help/jeecgback/topwrite/assets/image_1687780373126.png)\n\n\n\n\n\n\n\n', 'complete', '{\"filePath\":\"temp/index_1741335996542.md\"}');
INSERT INTO `airag_knowledge_doc` VALUES ('1897926933086261249', 'jeecg', '2025-03-07 16:26:57', 'jeecg', '2025-03-10 17:28:42', 'A04', NULL, '1897926563148648449', 'qa', 'file', '1.菜单的这些配置是什么意思？\n\n![](/static/jimuImages/screenshot_1585040135427.png)\n|    配置|    描述 |\n| --- | --- |\n|  是否路由菜单   |   是：跳转路由的时候根据配置的前端组件值跳转，否：起作用的是菜单路径  |\n|  隐藏路由   |   是：左侧菜单不加载反之加载  |\n|  缓存路由   |   是：路由只加载一次即created只执行一次  |\n|  聚合路由   |  是：只要配置在该路由下面的子路由全部不会显示在左侧菜单栏  |\n| 打开方式   |  内部打开是在窗口tab里打开，外部打开浏览器tab打开 |\n\n---\n2.列表页面跳转新的路由需要展示成面包屑菜单样式：\n目前不支持，需要自行扩展\n\n---\n3.表单设计器自定义扩展\n目前只支持将设计好的表单引入自己的modal页面，扩展暂不支持\n\n---\n4.图表点击事件\n有自定义的图表js增强事件，后续补充该文档\n\n---\n<span>20200324 LOWCOD-323</span>\n\n---\n\n5.online报表 系统变量的使用\n`select username,id from sys_user  where username = \'#{sys_user_code}\'`\n\n6.首页怎么改成自己的。\n方法一：直接修改文件：src/views/dashboard/Analysis.vue\n方法二：自定义首页页面，将首页菜单的前端组件配置为自己的文件，注意**只能修改前端组件不可修改菜单路径**\n\n![](/static/jimuImages/screenshot_1586254248894.png)\n\n\n\n7.项目编译 文件上有红色波浪线 ，点开文件红线消失，查看problem报错 xxx程序包不存在,实际该包存在\n解决方法：在Terminal 中执行 `mvn idea:idea` 再次编译即可\n\n\n\n\n', 'complete', '{\"filePath\":\"temp/QA_1741336015236.md\"}');
INSERT INTO `airag_knowledge_doc` VALUES ('1897932000963092482', 'jeecg', '2025-03-07 16:47:06', 'jeecg', '2025-03-07 16:47:10', 'A04', NULL, '1897212906878009346', 'index', 'file', '# 项目介绍\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/jimureport2.jpg)\n\n*****\n\n# JimuReport\n\n 积木报表，是一款免费的数据可视化报表，含报表、仪表盘和大屏设计，像搭建积木一样完全在线设计！功能涵盖：数据报表、打印设计、图表报表、门户设计、大屏设计等！\n \n -   Web版报表设计器，类Excel操作风格，通过拖拽完成报表设计，所见即所得。\n -   大屏采用类word风格，可以随意拖动组件，想怎么设计怎么设计，可以像百度和阿里一样，设计出炫酷大屏！\n -   从 v1.9+ 起推出 JimuBI 产品，她的牛叉之处，同时支持仪表盘、大屏、门户 (支持交互)、移动.\n -   秉承\"简单、易用、专业\"的产品理念，极大的降低报表开发难度、缩短开发周期、节省成本。\n -   领先的企业级Web报表，支持各种复杂报表，专注于解决企业报表难题。\n -   积木BI 数据可视化，支持大屏设计和仪表盘，致力于更生动、更友好的形式呈现实时业务数据分析\n\n```\n专注于开源，打造 “专业 易用 智能” 的数据可视化报表、大屏、门户\n开源协议：`功能免费、可以商用、代码不开放`\n```\n\n\n为什么选择 JimuReport?\n-----------------------------------\n>    永久免费，支持各种复杂报表，并且傻瓜式在线设计，非常的智能，低代码时代，这个是你的首选！\n\n- 采用SpringBoot的脚手架项目，都可以快速集成\n- Web 版设计器，类似于excel操作风格，通过拖拽完成报表设计\n- 通过SQL、API等方式，将数据源与模板绑定。同时支持表达式，自动计算合计等功能，使计算工作量大大降低\n- 开发效率很高，傻瓜式在线报表设计，一分钟设计一个报表，又简单又强大\n- 支持 ECharts，目前支持28种图表，在线拖拽设计，支持SQL和API两种数据源\n- 支持分组、交叉，合计、表达式等复杂报表\n- 支持打印设计（支持套打、背景打印等）可设置打印边距、方向、页眉页脚等参数 一键快速打印 同时可实现发票套打，不动产证等精准、无缝打印\n- 可视化图表，仪表盘设计器类大屏设计，支持丰富的数据源连接和移动端，通过拖拉拽方式快速制作图表和门户设计；支持多种图表类型：柱形图、折线图、散点图、饼图、环形图、面积图、漏斗图、进度图、仪表盘、雷达图、地图等等；\n- 可设计各种类型的单据、大屏，如出入库单、销售单、财务报表、合同、监控大屏、旅游数据大屏等\n- 大屏设计器支持几十种图表样式，可自由拼接、组合，设计炫酷大屏\n- 数据可视化，DataV、帆软的开源替代方案，比帆软拥有更好的体验和更简单的使用方式\n- [积木报表官网](http://jimureport.com/login) 可以在线免费制作报表和大屏，手机号一键注册，便可永久使用。大屏采用类word风格，可以随意拖动组件，想怎么设计怎么设计，可以像百度和阿里一样，设计出炫酷的可视化大屏！重要的是：免费！免费！免费！\n\n\n\n\n## 产生背景\n报表是企业IT服务必备的一项需求，但是行业内并没有一个免费好用的报表，大部分免费的报表功能较弱也不够智能，商业报表又很贵，所以有了研发一套免费报表的初衷。\n做一个什么样的报表呢？随着低代码概念的兴起，原先通过报表工具设计模板，再与系统集成的模式已经落伍，现在追求的是完全在线设计，傻瓜式的操作，实现简单易用又智能的报表！\n\n- 目前积木报表已经实现了完全在线设计，轻量级集成、类似excel的风格，像搭建积木一样在线拖拽设计报表！功能涵盖数据报表设计、打印设计、图表设计、门户设计、大屏设计等！\n- 2019年底启动积木报表研发工作，历经一年多的时间，2020-11-03第一版出炉 [v1.0-beta](https://www.oschina.net/news/119666/jimureport-1-0-beta-released)\n- 2020年的持续打磨和研发，终于在2021-1-18发布了第一个正式版本 [v1.1.05](https://www.oschina.net/news/126916/jimureport-1-1-05-released)\n- 截止到当前2024-09-14，积木报表已经完全涵盖商业BI的所有功能，包括不限于复杂报表、图表可视化、大屏、移动图表、填报等高级功能，而且拥有更好的体验和更简单的使用方式。\n- 更多版本日志查看 [版本日志](http://jimureport.com/doc/log)\n\n\n\n\n\n\n开发文档\n-----------------------------------\n\n- [快速集成](</quick.md>)\n- [集成源码下载](https://github.com/jeecgboot/JimuReport)\n- [大屏与报表演示](http://jimureport.com/login)  |  [零代码体验](https://app.qiaoqiaoyun.com)\n\n\n\n\n\n\n项目介绍\n-----------------------------------\n\n- 官方网站： http://www.jimureport.com\n- 视频教程： http://jimureport.com/doc/video\n- QQ交流群：③596660273、其他群(满)\n\n\n数据库兼容 \n-----------------------------------\n> 支持国产、常规、Nosql等30多种数据源，支持以SQL的方式去查询csv、mogodb等非物理数据库。\n\n|  数据库   |  支持   |\n| --- | --- |\n|   MySQL   |  √   |\n|  Oracle、Oracle9i   |  √   |\n|  SqlServer、SqlServer2012   |  √   |\n|   PostgreSQL   |  √   |\n|   DB2、Informix   |  √   |\n|   MariaDB   |  √   |\n|  SQLite、Hsqldb、Derby、H2   |  √   |\n|   达梦、人大金仓、神通   |  √   |\n|   华为高斯、虚谷、瀚高数据库、 TDengine 涛思数据   |  √   |\n|   阿里云PolarDB、PPAS、HerdDB   |  √   |\n|  Hive、HBase、CouchBase   |  √   |\n|  导入Excel、csv、json文件数据集   |  √   |\n|  sqllite、TiDB、Doris、clickhouse、 MongoDB-BI   |  √   |\n|  elasticsearch、mogodb  |  √   |\n\n\n\n报表设计效果\n-----------------------------------\n\n- 报表设计器（完全在线设计，简单易用）\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/up-752b454f64ed87c798b3e8a083fbd6622d4.gif)\n\n- 打印设计（支持套打、背景打印）\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687862827604.png)\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687862839013.png)\n\n- 数据报表（支持分组、交叉，合计等复杂报表）\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687862854011.png)\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687862862414.png)\n\n- 图形报表（目前支持28种图表）\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687862883559.png)\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687862892649.png)\n\n\n\n大屏设计效果\n-----------------------------------\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687862905901.png)\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687862938863.png)\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687862951297.png)\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687862960053.png)\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687862974786.png)\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687862983740.png)\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687862996008.png)\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687863002758.png)\n\n\n仪表盘设计器\n-----------------------------------\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687863014429.png)\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687863021555.png)\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687863028545.png)\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687863043320.png)\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687863050461.png)\n\n![](https://upload.jeecg.com/jeecg/help/jimureport/topwrite/assets/image_1687863057103.png)\n\n## 功能清单\n```\n├─报表设计器\n│  ├─数据源\n│  │  ├─支持多种数据源，如Oracle,MySQL,SQLServer,PostgreSQL等主流的数据库\n│  │  ├─支持SQL编写页面智能化，可以看到数据源下面的表清单和字段清单\n│  │  ├─支持参数\n│  │  ├─支持单数据源和多数数据源设置\n│  ├─单元格格式\n│  │  ├─边框\n│  │  ├─字体大小\n│  │  ├─字体颜色\n│  │  ├─背景色\n│  │  ├─字体加粗\n│  │  ├─支持水平和垂直的分散对齐\n│  │  ├─支持文字自动换行设置\n│  │  ├─图片设置为图片背景\n│  │  ├─支持无线行和无限列\n│  │  ├─支持设计器内冻结窗口\n│  │  ├─支持对单元格内容或格式的复制、粘贴和删除等功能\n│  │  ├─等等\n│  ├─报表元素\n│  │  ├─文本类型：直接写文本；支持数值类型的文本设置小数位数\n│  │  ├─图片类型：支持上传一张图表；支持图片动态生成\n│  │  ├─图表类型\n│  │  ├─函数类型\n│  │  └─支持求和\n│  │  └─平均值\n│  │  └─最大值\n│  │  └─最小值\n│  ├─背景\n│  │  ├─背景颜色设置\n│  │  ├─背景图片设置\n│  │  ├─背景透明度设置\n│  │  ├─背景大小设置\n│  ├─数据字典\n│  ├─报表打印\n│  │  ├─自定义打印\n│  │  └─医药笺、逮捕令、介绍信等自定义样式设计打印\n│  │  ├─简单数据打印\n│  │  └─出入库单、销售表打印\n│  │  └─带参数打印\n│  │  └─分页打印\n│  │  ├─套打\n│  │  └─不动产证书打印\n│  │  └─发票打印\n│  ├─数据报表\n│  │  ├─分组数据报表\n│  │  └─横向数据分组\n│  │  └─纵向数据分组\n│  │  └─多级循环表头分组\n│  │  └─横向分组小计\n│  │  └─纵向分组小计（预计2021.03.08）\n│  │  └─合计\n│  │  ├─交叉报表\n│  │  ├─明细表\n│  │  ├─带条件查询报表\n│  │  ├─表达式报表\n│  │  ├─带二维码/条形码报表\n│  │  ├─多表头复杂报表（预计2021.03.08发布）\n│  │  ├─主子报表（预计2021.03.08发布）\n│  │  ├─预警报表（预计2021.03.08发布）\n│  │  ├─数据钻取报表（预计2021.03.08发布）\n│  ├─图形报表\n│  │  ├─柱形图\n│  │  ├─折线图\n│  │  ├─饼图\n│  │  ├─折柱图\n│  │  ├─散点图\n│  │  ├─漏斗图\n│  │  ├─雷达图\n│  │  ├─象形图\n│  │  ├─地图\n│  │  ├─仪盘表\n│  │  ├─关系图\n│  │  ├─图表背景\n│  │  ├─图表动态刷新\n│  │  ├─图表数据字典\n│  ├─参数\n│  │  ├─参数配置\n│  │  ├─参数管理\n│  ├─导入导出\n│  │  ├─支持导入Excel\n│  │  ├─支持导出Excel、pdf；支持导出excel、pdf带参数\n│  ├─打印设置\n│  │  ├─打印区域设置\n│  │  ├─打印机设置\n│  │  ├─预览\n│  │  ├─打印页码设置\n├─大屏设计器\n│  ├─系统功能\n│  │  ├─静态数据源和动态数据源设置\n│  │  ├─基础功能\n│  │  └─支持拖拽设计\n│  │  └─支持增、删、改、查大屏\n│  │  └─支持复制大屏数据和样式\n│  │  └─支持大屏预览、分享\n│  │  └─支持系统自动保存数据，同时支持手动恢复数据\n│  │  └─支持设置大屏密码\n│  │  └─支持对组件图层的删除、组合、上移、下移、置顶、置底等\n│  │  ├─背景设置\n│  │  └─大屏的宽度和高度设置\n│  │  └─大屏简介设置\n│  │  └─背景颜色、背景图片设置\n│  │  └─封面图设置\n│  │  └─缩放比例设置\n│  │  └─环境地址设置\n│  │  └─水印设置\n│  │  ├─地图设置\n│  │  └─添加地图\n│  │  └─地图数据隔离\n│  ├─图表\n│  │  ├─柱形图\n│  │  ├─折线图\n│  │  ├─折柱图\n│  │  ├─饼图\n│  │  ├─象形图\n│  │  ├─雷达图\n│  │  ├─散点图\n│  │  ├─漏斗图\n│  │  ├─文本框\n│  │  ├─跑马灯\n│  │  ├─超链接\n│  │  ├─实时时间\n│  │  ├─地图\n│  │  ├─全国物流地图\n│  │  ├─地理坐标地图\n│  │  ├─城市派件地图\n│  │  ├─图片\n│  │  ├─图片框\n│  │  ├─轮播图\n│  │  ├─滑动组件\n│  │  ├─iframe\n│  │  ├─video\n│  │  ├─翻牌器\n│  │  ├─环形图\n│  │  ├─进度条\n│  │  ├─仪盘表\n│  │  ├─字浮云\n│  │  ├─表格\n│  │  ├─选项卡\n│  │  ├─万能组件\n└─其他模块\n   └─更多功能开发中。。\n```\n\n \n\n', 'complete', '{\"filePath\":\"temp/readme_1741337223240.md\"}');
INSERT INTO `airag_knowledge_doc` VALUES ('1905186930719539201', 'jeecg', '2025-03-27 17:15:36', 'jeecg', '2025-03-27 17:15:43', 'A04', NULL, '1905186756806918146', 'part1', 'file', NULL, 'complete', '{\"filePath\":\"https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/01第一部分第1章_1743066923748.pdf\"}');
INSERT INTO `airag_knowledge_doc` VALUES ('1905186968325668866', 'jeecg', '2025-03-27 17:15:45', 'jeecg', '2025-03-27 17:15:48', 'A04', NULL, '1905186756806918146', 'part2', 'file', NULL, 'complete', '{\"filePath\":\"https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/02第一部分第2章_1743066943040.pdf\"}');
INSERT INTO `airag_knowledge_doc` VALUES ('1905187001037045761', 'jeecg', '2025-03-27 17:15:52', 'jeecg', '2025-03-27 17:15:57', 'A04', NULL, '1905186756806918146', 'part3', 'file', NULL, 'complete', '{\"filePath\":\"https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/03第一部分第3章_1743066951733.pdf\"}');
INSERT INTO `airag_knowledge_doc` VALUES ('1905187042770370561', 'jeecg', '2025-03-27 17:16:02', 'jeecg', '2025-03-27 17:16:07', 'A04', NULL, '1905186756806918146', 'part4', 'file', NULL, 'complete', '{\"filePath\":\"https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/04第一部分第4章_1743066960385.pdf\"}');
INSERT INTO `airag_knowledge_doc` VALUES ('1905187113339535361', 'jeecg', '2025-03-27 17:16:19', 'jeecg', '2025-03-27 17:16:25', 'A04', NULL, '1905186756806918146', 'part5', 'file', NULL, 'complete', '{\"filePath\":\"https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/05第一部分第5章_1743066977792.pdf\"}');
INSERT INTO `airag_knowledge_doc` VALUES ('1905187163981561857', 'jeecg', '2025-03-27 17:16:31', 'jeecg', '2025-03-27 17:16:39', 'A04', NULL, '1905186756806918146', 'part6', 'file', NULL, 'complete', '{\"filePath\":\"https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/06第一部分第6章_1743066990164.pdf\"}');
INSERT INTO `airag_knowledge_doc` VALUES ('1905187246412218369', 'jeecg', '2025-03-27 17:16:51', 'jeecg', '2025-03-27 17:16:54', 'A04', NULL, '1905186756806918146', 'part7', 'file', NULL, 'complete', '{\"filePath\":\"https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/07第一部分第7章_1743067007831.pdf\"}');
INSERT INTO `airag_knowledge_doc` VALUES ('1905187348543520770', 'jeecg', '2025-03-27 17:17:15', 'jeecg', '2025-03-27 17:17:20', 'A04', NULL, '1905186756806918146', 'part8', 'file', NULL, 'complete', '{\"filePath\":\"https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/08第一部分第8章_1743067032663.pdf\"}');
INSERT INTO `airag_knowledge_doc` VALUES ('1905187596229754881', 'jeecg', '2025-03-27 17:18:14', 'jeecg', '2025-03-27 17:18:21', 'A04', NULL, '1905186756806918146', 'part9', 'file', NULL, 'complete', '{\"filePath\":\"https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/09第一部分第9章_1743067087019.pdf\"}');
INSERT INTO `airag_knowledge_doc` VALUES ('1905187666618564609', 'jeecg', '2025-03-27 17:18:31', 'jeecg', '2025-03-27 17:18:34', 'A04', NULL, '1905186756806918146', 'part10', 'file', NULL, 'complete', '{\"filePath\":\"https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/10第一部分第10章_1743067109769.pdf\"}');
INSERT INTO `airag_knowledge_doc` VALUES ('1905187818494312449', 'jeecg', '2025-03-27 17:19:07', 'jeecg', '2025-03-27 17:19:15', 'A04', NULL, '1905186756806918146', 'part11', 'file', NULL, 'complete', '{\"filePath\":\"https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/11第一部分第11章_1743067121732.pdf\"}');
INSERT INTO `airag_knowledge_doc` VALUES ('1905187888337862657', 'jeecg', '2025-03-27 17:19:24', 'jeecg', '2025-03-27 17:19:31', 'A04', NULL, '1905186756806918146', 'part12', 'file', NULL, 'complete', '{\"filePath\":\"https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/12第一部分第12章_1743067158952.pdf\"}');
INSERT INTO `airag_knowledge_doc` VALUES ('1905187920491397122', 'jeecg', '2025-03-27 17:19:32', 'jeecg', '2025-03-27 17:19:38', 'A04', NULL, '1905186756806918146', 'part13', 'file', NULL, 'complete', '{\"filePath\":\"https://jeecgdev.oss-cn-beijing.aliyuncs.com/temp/13第一部分第13章_1743067170886.pdf\"}');

-- ----------------------------
-- Table structure for airag_model
-- ----------------------------
CREATE TABLE `airag_model`  (
                                `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                                `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建人',
                                `create_time` datetime NULL DEFAULT NULL COMMENT '创建日期',
                                `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新人',
                                `update_time` datetime NULL DEFAULT NULL COMMENT '更新日期',
                                `sys_org_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '所属部门',
                                `tenant_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '租户id',
                                `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '名称',
                                `provider` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '供应者',
                                `model_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模型名称',
                                `credential` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '凭证信息',
                                `base_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'API域名',
                                `model_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模型类型',
                                `model_params` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模型参数',
                                PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of airag_model
-- ----------------------------
INSERT INTO `airag_model` VALUES ('1890232564262739969', 'jeecg', '2025-02-14 10:52:16', 'admin', '2025-04-02 22:20:37', 'A04', NULL, 'OpenAI', 'OPENAI', 'gpt-4o-mini', '{\"apiKey\":\"sk-cgQRNc3mWb3YtdO9C0F6AcBc86\"}', 'https://api.gpt.ge', 'LLM', '{\"temperature\":0.2,\"topP\":0.7,\"presencePenalty\":0.5,\"frequencyPenalty\":0.5,\"maxTokens\":null}');
INSERT INTO `airag_model` VALUES ('1891459707122499586', 'jeecg', '2025-02-17 20:08:30', 'admin', '2025-04-02 22:20:34', 'A04', NULL, 'OpenAI向量', 'OPENAI', 'text-embedding-ada-002', '{\"apiKey\":\"sk-cgQRNc3mWb3YtdO9C0F6Ac\"}', 'https://api.v3.cm/v1', 'EMBED', NULL);
INSERT INTO `airag_model` VALUES ('1897481367743143938', 'jeecg', '2025-03-06 10:56:26', 'admin', '2025-04-02 22:20:31', 'A04', NULL, 'deepseek', 'DEEPSEEK', 'deepseek-chat', '{\"apiKey\":\"sk-ff138aa9896945468ec\"}', 'https://api.deepseek.com/v1', 'LLM', NULL);
INSERT INTO `airag_model` VALUES ('1897883052995006466', 'jeecg', '2025-03-07 13:32:35', 'admin', '2025-04-02 23:53:33', 'A04', NULL, '智谱', 'ZHIPU', 'glm-4-flash', '{\"apiKey\":\"522f6486bc6944b2ba346f054c0184e0.\"}', 'https://open.bigmodel.cn/', 'LLM', NULL);
INSERT INTO `airag_model` VALUES ('1897884353107611650', 'jeecg', '2025-03-07 13:37:45', 'admin', '2025-04-02 22:20:22', 'A04', NULL, '智谱向量', 'ZHIPU', 'Embedding-3', '{\"apiKey\":\"522f6486bc6944b2ba346f054c0184e0.\"}', 'https://open.bigmodel.cn', 'EMBED', '{\"temperature\":0.7,\"topP\":0.7,\"presencePenalty\":null,\"frequencyPenalty\":null,\"maxTokens\":null}');

SET FOREIGN_KEY_CHECKS = 1;


-- ----------------------------
-- Records of sys_dict
-- ----------------------------

INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `tenant_id`, `low_app_id`) VALUES ('1894701158027554818', 'AI应用类型', 'ai_app_type', NULL, 0, 'jeecg', '2025-02-26 18:48:53', NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `tenant_id`, `low_app_id`) VALUES ('1891672414555860993', '知识库文档类型', 'know_doc_type', NULL, 0, 'jeecg', '2025-02-18 10:13:44', NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `tenant_id`, `low_app_id`) VALUES ('1891671216561975297', '知识库类型', 'airag_know_type', NULL, 1, 'jeecg', '2025-02-18 10:08:58', NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `tenant_id`, `low_app_id`) VALUES ('1891456510739890177', '模型类型', 'model_type', NULL, 0, 'jeecg', '2025-02-17 19:55:48', NULL, NULL, 0, 0, NULL);
INSERT INTO `sys_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `tenant_id`, `low_app_id`) VALUES ('1890229208685322242', '模型提供者', 'model_provider', NULL, 0, 'jeecg', '2025-02-14 10:38:57', NULL, NULL, 0, 0, NULL);

-- ----------------------------
-- Records of sys_dict_item
-- ----------------------------

INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `item_color`) VALUES ('1894701332930031618', '1894701158027554818', '高级编排', 'chatFLow', NULL, 2, 1, 'jeecg', '2025-02-26 18:49:34', NULL, NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `item_color`) VALUES ('1894701277019959298', '1894701158027554818', '简单配置', 'chatSimple', NULL, 1, 1, 'jeecg', '2025-02-26 18:49:21', NULL, NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `item_color`) VALUES ('1891672567924781058', '1891672414555860993', '网页', 'web', NULL, 1, 1, 'jeecg', '2025-02-18 10:14:20', NULL, NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `item_color`) VALUES ('1891672540963794946', '1891672414555860993', '文件', 'file', NULL, 1, 1, 'jeecg', '2025-02-18 10:14:14', NULL, NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `item_color`) VALUES ('1891672501432479746', '1891672414555860993', '文本', 'text', NULL, 1, 1, 'jeecg', '2025-02-18 10:14:05', NULL, NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `item_color`) VALUES ('1891458099609354241', '1891456510739890177', '向量模型', 'EMBED', NULL, 1, 1, 'jeecg', '2025-02-17 20:02:07', 'jeecg', '2025-02-17 20:39:01', NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `item_color`) VALUES ('1891456733029613569', '1891456510739890177', '语言模型', 'LLM', NULL, 1, 1, 'jeecg', '2025-02-17 19:56:41', 'jeecg', '2025-02-17 20:02:15', NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `item_color`) VALUES ('1890230437670920194', '1890229208685322242', 'Ollama', 'OLLAMA', NULL, 1, 1, 'jeecg', '2025-02-14 10:43:50', NULL, NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `item_color`) VALUES ('1890230384159989762', '1890229208685322242', 'DeepSeek', 'DEEPSEEK', NULL, 1, 1, 'jeecg', '2025-02-14 10:43:37', NULL, NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `item_color`) VALUES ('1890230305948803073', '1890229208685322242', '通义千问', 'QWEN', NULL, 1, 1, 'jeecg', '2025-02-14 10:43:18', NULL, NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `item_color`) VALUES ('1890230107835047937', '1890229208685322242', '千帆大模型', 'QIANFAN', NULL, 1, 1, 'jeecg', '2025-02-14 10:42:31', NULL, NULL, NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `item_color`) VALUES ('1890230018852888577', '1890229208685322242', '智谱AI', 'ZHIPU', NULL, 1, 1, 'jeecg', '2025-02-14 10:42:10', 'jeecg', '2025-02-14 10:42:42', NULL);
INSERT INTO `sys_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`, `item_color`) VALUES ('1890229967585910786', '1890229208685322242', 'OpenAI', 'OPENAI', NULL, 1, 1, 'jeecg', '2025-02-14 10:41:58', 'jeecg', '2025-02-14 10:42:48', NULL);
